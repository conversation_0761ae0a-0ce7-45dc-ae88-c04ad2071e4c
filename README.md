# NMODM 2.0 - Python + QML Application Framework

A modern Python application framework using PySide6 and QML for creating desktop applications with beautiful, responsive user interfaces.

## Features

### Core Framework
- **Frameless Window**: Modern borderless window design with custom title bar
- **Drag & Drop**: Built-in window dragging functionality
- **Python-QML Bridge**: Seamless communication between Python backend and QML frontend
- **Resource Management**: Organized resource loading and management system
- **Modular Architecture**: Clean separation of concerns with core modules
- **Configuration Management**: JSON-based configuration with auto-save functionality

### ME3 Mod Manager
- **Game Detection**: Automatic detection of FROMSOFTWARE games (Elden Ring, Sekiro, Armored Core VI)
- **Mod Scanning**: Intelligent scanning and categorization of mod files
- **Mod Management**: Enable/disable mods with ME3 configuration integration
- **Real-time Monitoring**: Game process and mod loading status monitoring
- **TOML Configuration**: Full ME3 configuration file management
- **Multi-game Support**: Support for multiple FROMSOFTWARE titles

### EasyTier P2P VPN Manager
- **Process Management**: Start/stop EasyTier processes with configuration management
- **Network Monitoring**: Real-time P2P network status and peer monitoring
- **Connection Handling**: Automatic reconnection and failover mechanisms
- **Status Monitoring**: Network health, latency, and performance metrics
- **Exception Handling**: Intelligent error recovery and connection restoration
- **Async Operations**: Non-blocking network operations with real-time updates

## Project Structure

```
src/
├── nmodm/
│   ├── __init__.py
│   ├── main.py              # Application entry point
│   ├── core/
│   │   ├── __init__.py
│   │   ├── application.py   # Main application class
│   │   ├── resource_manager.py  # Resource management
│   │   └── window_manager.py    # Window operations and dragging
│   ├── models/
│   │   ├── __init__.py
│   │   └── app_model.py     # Application data model
│   ├── me3/                 # ME3 mod management
│   │   ├── __init__.py
│   │   ├── game_detector.py # Game installation detection
│   │   ├── mod_scanner.py   # Mod file scanning
│   │   ├── mod_manager.py   # Mod enable/disable management
│   │   └── mod_monitor.py   # Real-time monitoring
│   └── easytier/            # EasyTier P2P VPN management
│       ├── __init__.py
│       ├── process_manager.py    # Process lifecycle management
│       ├── network_manager.py    # P2P network management
│       ├── status_monitor.py     # Real-time status monitoring
│       └── connection_handler.py # Connection exception handling
resources/
├── qml/
│   ├── main.qml            # Main window QML
│   └── WindowControlButton.qml  # Custom window controls
├── images/                 # Image resources
└── icons/                  # Application icons
```

## Installation

1. **Clone or download the project**

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Install in development mode** (optional):
   ```bash
   pip install -e .
   ```

## Running the Application

### Method 1: Using the convenience script
```bash
python run.py
```

### Method 2: QtWidgets Demo (Working)
```bash
python demo_widgets.py
```

### Method 3: Direct execution
```bash
python -m src.nmodm.main
```

### Method 4: Using the installed script (if installed with pip)
```bash
nmodm-app
```

### Method 5: From the source directory
```bash
cd src
python -m nmodm.main
```

**Note**: The project works with PySide6 6.6.0. Both QML and QtWidgets versions are fully functional.

## Development

### Adding New QML Components

1. Create your QML file in `resources/qml/`
2. Import it in your main QML files as needed
3. Register any new Python types in `main.py`

### Adding New Python Models

1. Create your model class in `src/nmodm/models/`
2. Inherit from `QObject` and use `@QmlElement` decorator
3. Register the type in `main.py` using `qmlRegisterType`

### Example: Creating a New Model

```python
from PySide6.QtCore import QObject, Signal, Slot, Property
from PySide6.QtQml import QmlElement

QML_IMPORT_NAME = "NmodmApp"
QML_IMPORT_MAJOR_VERSION = 1

@QmlElement
class MyModel(QObject):
    dataChanged = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._data = ""
    
    @Property(str, notify=dataChanged)
    def data(self):
        return self._data
    
    @Slot(str)
    def setData(self, value):
        if self._data != value:
            self._data = value
            self.dataChanged.emit()
```

## Configuration

The application supports JSON-based configuration files. Configuration is automatically saved every 30 seconds and can be manually saved/loaded through the AppModel.

## Window Features

- **Frameless Design**: Custom title bar with minimize, maximize, and close buttons
- **Dragging**: Click and drag the title bar to move the window
- **Double-click Maximize**: Double-click the title bar to toggle maximize/restore
- **Rounded Corners**: Modern UI with rounded window corners and drop shadow

## Customization

### Changing Window Appearance

Edit `resources/qml/main.qml` to modify:
- Window size and minimum size
- Colors and styling
- Layout and components

### Adding New Features

1. Create new Python classes in appropriate modules
2. Register them with QML using `@QmlElement` decorator
3. Use them in QML files by importing `NmodmApp 1.0`

## Requirements

- Python 3.8+
- PySide6 6.6.0
- toml >= 0.10.2
- psutil >= 5.9.0

## License

MIT License - see LICENSE file for details.
