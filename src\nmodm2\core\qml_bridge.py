"""
QML Bridge for Python-QML communication
"""

import logging
from typing import Any, Optional

from PySide6.QtCore import QObject, Signal, Slot, Property, QTimer
from PySide6.QtGui import QGuiApplication


class QMLBridge(QObject):
    """Bridge class for Python-QML communication."""
    
    # Signals
    messageReceived = Signal(str, 'QVariant')  # type, data
    statusChanged = Signal(str, str)  # component, status
    errorOccurred = Signal(str, str)  # component, error
    
    def __init__(self, parent: Optional[QObject] = None):
        super().__init__(parent)
        self._logger = logging.getLogger(__name__)
        self._app_version = "2.0.0"
        self._is_debug = False
        
        # Window properties
        self._window_title = "NModM 2.0"
        self._window_width = 1200
        self._window_height = 800
        self._window_minimized = False
        self._window_maximized = False
    
    @Slot(str, 'QVariant')
    def sendMessage(self, message_type: str, data: Any = None) -> None:
        """Send message from QML to Python."""
        self._logger.debug(f"Received message: {message_type}, data: {data}")
        self.messageReceived.emit(message_type, data)
    
    @Slot(str, str)
    def logMessage(self, level: str, message: str) -> None:
        """Log message from QML."""
        log_level = getattr(logging, level.upper(), logging.INFO)
        self._logger.log(log_level, f"QML: {message}")
    
    @Slot(result=bool)
    def isDebugMode(self) -> bool:
        """Check if debug mode is enabled."""
        return self._is_debug
    
    @Slot(bool)
    def setDebugMode(self, debug: bool) -> None:
        """Set debug mode."""
        self._is_debug = debug
    
    @Slot()
    def minimizeWindow(self) -> None:
        """Minimize the application window."""
        app = QGuiApplication.instance()
        if app and app.allWindows():
            window = app.allWindows()[0]
            window.showMinimized()
            self._window_minimized = True
            self.windowStateChanged.emit()
    
    @Slot()
    def maximizeWindow(self) -> None:
        """Maximize/restore the application window."""
        app = QGuiApplication.instance()
        if app and app.allWindows():
            window = app.allWindows()[0]
            if self._window_maximized:
                window.showNormal()
                self._window_maximized = False
            else:
                window.showMaximized()
                self._window_maximized = True
            self.windowStateChanged.emit()
    
    @Slot()
    def closeWindow(self) -> None:
        """Close the application window."""
        app = QGuiApplication.instance()
        if app:
            app.quit()
    
    @Slot(int, int)
    def setWindowSize(self, width: int, height: int) -> None:
        """Set window size."""
        self._window_width = width
        self._window_height = height
        
        app = QGuiApplication.instance()
        if app and app.allWindows():
            window = app.allWindows()[0]
            window.resize(width, height)
    
    @Slot(str)
    def setWindowTitle(self, title: str) -> None:
        """Set window title."""
        self._window_title = title
        
        app = QGuiApplication.instance()
        if app and app.allWindows():
            window = app.allWindows()[0]
            window.setTitle(title)
    
    @Slot(str, str)
    def showNotification(self, title: str, message: str) -> None:
        """Show system notification."""
        # TODO: Implement system notifications
        self._logger.info(f"Notification: {title} - {message}")
    
    @Slot(str, result=str)
    def getEnvironmentVariable(self, name: str) -> str:
        """Get environment variable."""
        import os
        return os.environ.get(name, "")
    
    @Slot(str, result=bool)
    def fileExists(self, path: str) -> bool:
        """Check if file exists."""
        from pathlib import Path
        return Path(path).exists()
    
    @Slot(str, result=bool)
    def directoryExists(self, path: str) -> bool:
        """Check if directory exists."""
        from pathlib import Path
        return Path(path).is_dir()
    
    @Slot(str, result=str)
    def getFileSize(self, path: str) -> str:
        """Get file size in human readable format."""
        from pathlib import Path
        try:
            size = Path(path).stat().st_size
            for unit in ['B', 'KB', 'MB', 'GB']:
                if size < 1024.0:
                    return f"{size:.1f} {unit}"
                size /= 1024.0
            return f"{size:.1f} TB"
        except:
            return "Unknown"
    
    # Properties
    @Property(str, constant=True)
    def appVersion(self) -> str:
        return self._app_version
    
    @Property(str, notify=None)
    def windowTitle(self) -> str:
        return self._window_title
    
    @Property(int, notify=None)
    def windowWidth(self) -> int:
        return self._window_width
    
    @Property(int, notify=None)
    def windowHeight(self) -> int:
        return self._window_height
    
    windowStateChanged = Signal()

    @Property(bool, notify=windowStateChanged)
    def isMinimized(self) -> bool:
        return self._window_minimized

    @Property(bool, notify=windowStateChanged)
    def isMaximized(self) -> bool:
        return self._window_maximized
