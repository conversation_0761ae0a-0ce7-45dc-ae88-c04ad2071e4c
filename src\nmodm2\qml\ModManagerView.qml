import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Controls.Material 2.15
import QtQuick.Layouts 1.15
import NModM 1.0

ColumnLayout {
    id: root
    spacing: 16
    
    // Header
    RowLayout {
        Layout.fillWidth: true
        spacing: 16
        
        Text {
            text: "ME3 Mod Manager"
            color: ThemeManager.getColor("textPrimary")
            font.pixelSize: 24
            font.weight: Font.Bold
            Layout.fillWidth: true
        }
        
        Button {
            text: "Refresh"
            onClicked: modManager.refreshMods()
            enabled: modManager.isInitialized
        }
        
        Button {
            text: "Settings"
            onClicked: modSettingsDialog.open()
        }
    }
    
    // Status bar
    Rectangle {
        Layout.fillWidth: true
        height: 60
        color: ThemeManager.getColor("surface")
        radius: 8
        
        RowLayout {
            anchors.fill: parent
            anchors.margins: 16
            spacing: 24
            
            RowLayout {
                spacing: 8
                
                Rectangle {
                    width: 12
                    height: 12
                    radius: 6
                    color: modManager.isInitialized ? "#4CAF50" : "#F44336"
                }
                
                Text {
                    text: modManager.isInitialized ? "Ready" : "Not initialized"
                    color: ThemeManager.getColor("textPrimary")
                    font.pixelSize: 14
                    font.weight: Font.Medium
                }
            }
            
            Text {
                text: "Game Path: " + (modManager.gamePath || "Not detected")
                color: ThemeManager.getColor("textSecondary")
                font.pixelSize: 12
                Layout.fillWidth: true
            }
            
            Text {
                text: modManager.enabledModCount + " / " + modManager.modCount + " mods enabled"
                color: ThemeManager.getColor("textPrimary")
                font.pixelSize: 14
            }
        }
    }
    
    // Mod list
    Rectangle {
        Layout.fillWidth: true
        Layout.fillHeight: true
        color: ThemeManager.getColor("surface")
        radius: 8
        
        ColumnLayout {
            anchors.fill: parent
            anchors.margins: 16
            spacing: 12
            
            // List header
            RowLayout {
                Layout.fillWidth: true
                spacing: 16
                
                Text {
                    text: "Installed Mods"
                    color: ThemeManager.getColor("textPrimary")
                    font.pixelSize: 16
                    font.weight: Font.Medium
                    Layout.fillWidth: true
                }
                
                TextField {
                    id: searchField
                    placeholderText: "Search mods..."
                    Layout.preferredWidth: 200
                }
            }
            
            // Mod list view
            ScrollView {
                Layout.fillWidth: true
                Layout.fillHeight: true
                
                ListView {
                    id: modListView
                    model: modManager.getModList()
                    spacing: 8
                    
                    delegate: Rectangle {
                        width: modListView.width
                        height: 80
                        color: ThemeManager.getColor("cardBackground")
                        radius: 8
                        border.color: ThemeManager.getColor("divider")
                        border.width: 1
                        
                        RowLayout {
                            anchors.fill: parent
                            anchors.margins: 16
                            spacing: 16
                            
                            // Mod info
                            ColumnLayout {
                                Layout.fillWidth: true
                                spacing: 4
                                
                                Text {
                                    text: modelData.name || "Unknown Mod"
                                    color: ThemeManager.getColor("textPrimary")
                                    font.pixelSize: 16
                                    font.weight: Font.Medium
                                }
                                
                                Text {
                                    text: "Version: " + (modelData.version || "Unknown") + 
                                          " | Author: " + (modelData.author || "Unknown")
                                    color: ThemeManager.getColor("textSecondary")
                                    font.pixelSize: 12
                                }
                                
                                Text {
                                    text: modelData.description || "No description available"
                                    color: ThemeManager.getColor("textSecondary")
                                    font.pixelSize: 12
                                    wrapMode: Text.WordWrap
                                    maximumLineCount: 2
                                    elide: Text.ElideRight
                                }
                            }
                            
                            // Status and controls
                            ColumnLayout {
                                spacing: 8
                                
                                Text {
                                    text: modelData.size_formatted || "Unknown size"
                                    color: ThemeManager.getColor("textSecondary")
                                    font.pixelSize: 12
                                    horizontalAlignment: Text.AlignRight
                                }
                                
                                Switch {
                                    checked: modelData.status === "enabled"
                                    onToggled: {
                                        modManager.setModEnabled(modelData.name, checked)
                                    }
                                }
                            }
                        }
                        
                        // Hover effect
                        MouseArea {
                            anchors.fill: parent
                            hoverEnabled: true
                            onEntered: parent.color = Qt.lighter(ThemeManager.getColor("cardBackground"), 1.1)
                            onExited: parent.color = ThemeManager.getColor("cardBackground")
                            onClicked: {
                                // Show mod details
                                modDetailsDialog.modData = modelData
                                modDetailsDialog.open()
                            }
                        }
                    }
                    
                    // Empty state
                    Label {
                        anchors.centerIn: parent
                        text: modManager.isInitialized ? 
                              "No mods found\nClick 'Refresh' to scan for mods" : 
                              "Mod manager not initialized\nCheck game path in settings"
                        color: ThemeManager.getColor("textSecondary")
                        font.pixelSize: 14
                        horizontalAlignment: Text.AlignHCenter
                        visible: modListView.count === 0
                    }
                }
            }
        }
    }
    
    // Mod details dialog
    Dialog {
        id: modDetailsDialog
        title: "Mod Details"
        modal: true
        anchors.centerIn: parent
        width: 500
        height: 400
        
        property var modData: ({})
        
        ScrollView {
            anchors.fill: parent
            
            ColumnLayout {
                width: modDetailsDialog.width - 40
                spacing: 16
                
                Text {
                    text: modDetailsDialog.modData.name || "Unknown Mod"
                    color: ThemeManager.getColor("textPrimary")
                    font.pixelSize: 20
                    font.weight: Font.Bold
                }
                
                GridLayout {
                    Layout.fillWidth: true
                    columns: 2
                    columnSpacing: 16
                    rowSpacing: 8
                    
                    Text { text: "Version:"; color: ThemeManager.getColor("textSecondary") }
                    Text { text: modDetailsDialog.modData.version || "Unknown"; color: ThemeManager.getColor("textPrimary") }
                    
                    Text { text: "Author:"; color: ThemeManager.getColor("textSecondary") }
                    Text { text: modDetailsDialog.modData.author || "Unknown"; color: ThemeManager.getColor("textPrimary") }
                    
                    Text { text: "Type:"; color: ThemeManager.getColor("textSecondary") }
                    Text { text: modDetailsDialog.modData.mod_type || "Unknown"; color: ThemeManager.getColor("textPrimary") }
                    
                    Text { text: "Size:"; color: ThemeManager.getColor("textSecondary") }
                    Text { text: modDetailsDialog.modData.size_formatted || "Unknown"; color: ThemeManager.getColor("textPrimary") }
                    
                    Text { text: "Status:"; color: ThemeManager.getColor("textSecondary") }
                    Text { 
                        text: modDetailsDialog.modData.status || "Unknown"
                        color: modDetailsDialog.modData.status === "enabled" ? "#4CAF50" : "#F44336"
                    }
                }
                
                Text {
                    text: "Description:"
                    color: ThemeManager.getColor("textSecondary")
                    font.weight: Font.Medium
                }
                
                Text {
                    text: modDetailsDialog.modData.description || "No description available"
                    color: ThemeManager.getColor("textPrimary")
                    wrapMode: Text.WordWrap
                    Layout.fillWidth: true
                }
                
                Text {
                    text: "Path: " + (modDetailsDialog.modData.path || "Unknown")
                    color: ThemeManager.getColor("textSecondary")
                    font.pixelSize: 12
                    wrapMode: Text.WordWrap
                    Layout.fillWidth: true
                }
            }
        }
    }
    
    // Mod settings dialog
    Dialog {
        id: modSettingsDialog
        title: "Mod Manager Settings"
        modal: true
        anchors.centerIn: parent
        width: 400
        height: 300
        
        ColumnLayout {
            anchors.fill: parent
            spacing: 16
            
            Text {
                text: "Game Path"
                color: ThemeManager.getColor("textPrimary")
                font.weight: Font.Medium
            }
            
            RowLayout {
                Layout.fillWidth: true
                
                TextField {
                    id: gamePathField
                    text: modManager.gamePath
                    placeholderText: "Select ME3 installation path..."
                    Layout.fillWidth: true
                }
                
                Button {
                    text: "Browse"
                    onClicked: {
                        // TODO: Open file dialog
                    }
                }
            }
            
            Button {
                text: "Auto-detect Game Path"
                Layout.fillWidth: true
                onClicked: {
                    // Trigger auto-detection
                    modManager.setGamePath("")
                }
            }
            
            Item { Layout.fillHeight: true }
            
            RowLayout {
                Layout.fillWidth: true
                
                Item { Layout.fillWidth: true }
                
                Button {
                    text: "Cancel"
                    onClicked: modSettingsDialog.close()
                }
                
                Button {
                    text: "Apply"
                    onClicked: {
                        if (gamePathField.text) {
                            modManager.setGamePath(gamePathField.text)
                        }
                        modSettingsDialog.close()
                    }
                }
            }
        }
    }
}
