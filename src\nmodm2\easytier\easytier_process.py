"""
EasyTier Process Management
"""

import logging
import subprocess
import threading
import time
from pathlib import Path
from typing import List, Optional

from PySide6.QtCore import QObject, Signal, QProcess, QTimer
import psutil


class EasyTierProcess(QObject):
    """Manages EasyTier process lifecycle."""
    
    # Signals
    processStateChanged = Signal(str)  # "starting", "running", "stopped", "error"
    logMessage = Signal(str, str)  # level, message
    errorOccurred = Signal(str, str)  # component, error
    
    def __init__(self, binary_path: str, parent: Optional[QObject] = None):
        super().__init__(parent)
        self._logger = logging.getLogger(__name__)
        self._binary_path = binary_path
        self._process: Optional[QProcess] = None
        self._pid: Optional[int] = None
        self._is_running = False
        
        # Process monitoring
        self._monitor_timer = QTimer()
        self._monitor_timer.timeout.connect(self._monitor_process)
        self._monitor_timer.setSingleShot(False)
    
    def start(self, args: List[str]) -> bool:
        """Start EasyTier process with given arguments."""
        try:
            if self._is_running:
                self._logger.warning("EasyTier process is already running")
                return False
            
            if not Path(self._binary_path).exists():
                self.errorOccurred.emit("Process", f"Binary not found: {self._binary_path}")
                return False
            
            self._logger.info(f"Starting EasyTier with args: {args}")
            self.processStateChanged.emit("starting")
            
            # Create QProcess
            self._process = QProcess()
            self._process.setProgram(self._binary_path)
            self._process.setArguments(args)
            
            # Connect signals
            self._process.started.connect(self._on_process_started)
            self._process.finished.connect(self._on_process_finished)
            self._process.errorOccurred.connect(self._on_process_error)
            self._process.readyReadStandardOutput.connect(self._on_stdout_ready)
            self._process.readyReadStandardError.connect(self._on_stderr_ready)
            
            # Start process
            self._process.start()
            
            # Wait for process to start
            if self._process.waitForStarted(5000):
                self._pid = self._process.processId()
                self._is_running = True
                self._monitor_timer.start(2000)  # Monitor every 2 seconds
                return True
            else:
                self.errorOccurred.emit("Process", "Failed to start EasyTier process")
                return False
                
        except Exception as e:
            self._logger.error(f"Failed to start EasyTier process: {e}")
            self.errorOccurred.emit("Process", str(e))
            return False
    
    def stop(self) -> bool:
        """Stop EasyTier process."""
        try:
            if not self._is_running:
                return True
            
            self._logger.info("Stopping EasyTier process")
            self.processStateChanged.emit("stopping")
            
            if self._process:
                # Try graceful termination first
                self._process.terminate()
                
                # Wait for process to terminate
                if self._process.waitForFinished(5000):
                    self._cleanup_process()
                    return True
                else:
                    # Force kill if graceful termination fails
                    self._process.kill()
                    self._process.waitForFinished(2000)
                    self._cleanup_process()
                    return True
            
            # Fallback: kill by PID if QProcess fails
            if self._pid:
                self._kill_by_pid(self._pid)
                self._cleanup_process()
                return True
            
            return False
            
        except Exception as e:
            self._logger.error(f"Failed to stop EasyTier process: {e}")
            self.errorOccurred.emit("Process", str(e))
            return False
    
    def is_running(self) -> bool:
        """Check if EasyTier process is running."""
        if not self._is_running:
            return False
        
        # Check if process is still alive
        if self._pid:
            try:
                process = psutil.Process(self._pid)
                return process.is_running() and process.status() != psutil.STATUS_ZOMBIE
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                self._is_running = False
                return False
        
        return False
    
    def get_pid(self) -> Optional[int]:
        """Get process PID."""
        return self._pid
    
    def get_process_info(self) -> dict:
        """Get detailed process information."""
        if not self._pid:
            return {}
        
        try:
            process = psutil.Process(self._pid)
            return {
                'pid': self._pid,
                'name': process.name(),
                'status': process.status(),
                'cpu_percent': process.cpu_percent(),
                'memory_info': process.memory_info()._asdict(),
                'create_time': process.create_time(),
                'cmdline': process.cmdline(),
            }
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            return {}
    
    def _on_process_started(self) -> None:
        """Handle process started signal."""
        self._logger.info("EasyTier process started successfully")
        self._is_running = True
        self.processStateChanged.emit("running")
        self.logMessage.emit("INFO", "EasyTier process started")
    
    def _on_process_finished(self, exit_code: int, exit_status) -> None:
        """Handle process finished signal."""
        self._logger.info(f"EasyTier process finished with exit code: {exit_code}")
        self._cleanup_process()
        
        if exit_code == 0:
            self.processStateChanged.emit("stopped")
            self.logMessage.emit("INFO", "EasyTier process stopped normally")
        else:
            self.processStateChanged.emit("error")
            self.logMessage.emit("ERROR", f"EasyTier process exited with code: {exit_code}")
    
    def _on_process_error(self, error) -> None:
        """Handle process error signal."""
        error_msg = f"EasyTier process error: {error}"
        self._logger.error(error_msg)
        self.processStateChanged.emit("error")
        self.errorOccurred.emit("Process", error_msg)
        self._cleanup_process()
    
    def _on_stdout_ready(self) -> None:
        """Handle stdout output."""
        if self._process:
            data = self._process.readAllStandardOutput().data().decode('utf-8', errors='ignore')
            for line in data.strip().split('\n'):
                if line.strip():
                    self.logMessage.emit("INFO", f"STDOUT: {line.strip()}")
    
    def _on_stderr_ready(self) -> None:
        """Handle stderr output."""
        if self._process:
            data = self._process.readAllStandardError().data().decode('utf-8', errors='ignore')
            for line in data.strip().split('\n'):
                if line.strip():
                    self.logMessage.emit("ERROR", f"STDERR: {line.strip()}")
    
    def _monitor_process(self) -> None:
        """Monitor process status."""
        if not self.is_running():
            if self._is_running:
                # Process died unexpectedly
                self._logger.warning("EasyTier process died unexpectedly")
                self.processStateChanged.emit("error")
                self.errorOccurred.emit("Process", "Process died unexpectedly")
                self._cleanup_process()
    
    def _cleanup_process(self) -> None:
        """Clean up process resources."""
        self._is_running = False
        self._pid = None
        self._monitor_timer.stop()
        
        if self._process:
            self._process.deleteLater()
            self._process = None
    
    def _kill_by_pid(self, pid: int) -> None:
        """Kill process by PID."""
        try:
            process = psutil.Process(pid)
            process.terminate()
            
            # Wait for termination
            try:
                process.wait(timeout=5)
            except psutil.TimeoutExpired:
                # Force kill if termination fails
                process.kill()
                process.wait(timeout=2)
                
        except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
            self._logger.debug(f"Failed to kill process {pid}: {e}")
    
    def __del__(self):
        """Destructor - ensure process is stopped."""
        if self._is_running:
            self.stop()
