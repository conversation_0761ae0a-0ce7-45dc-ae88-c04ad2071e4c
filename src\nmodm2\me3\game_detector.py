"""
ME3 Game Installation Detector
"""

import logging
import os
import sys
from pathlib import Path
from typing import Optional, List

if sys.platform == "win32":
    import winreg
else:
    winreg = None


class GameDetector:
    """Detects ME3 game installation paths."""
    
    def __init__(self):
        self._logger = logging.getLogger(__name__)
    
    def detect_me3_path(self) -> Optional[str]:
        """Detect ME3 installation path."""
        self._logger.info("Detecting ME3 installation...")
        
        # Try multiple detection methods
        detection_methods = [
            self._detect_from_registry,
            self._detect_from_steam,
            self._detect_from_origin,
            self._detect_from_common_paths,
        ]
        
        for method in detection_methods:
            try:
                path = method()
                if path and self._validate_me3_installation(path):
                    self._logger.info(f"Found ME3 at: {path}")
                    return path
            except Exception as e:
                self._logger.debug(f"Detection method failed: {e}")
        
        self._logger.warning("ME3 installation not found")
        return None
    
    def _detect_from_registry(self) -> Optional[str]:
        """Detect ME3 from Windows registry."""
        if sys.platform != "win32" or not winreg:
            return None
        
        registry_paths = [
            # Origin/EA App
            (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\BioWare\Mass Effect 3", "Install Dir"),
            (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Wow6432Node\BioWare\Mass Effect 3", "Install Dir"),
            
            # Steam
            (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\Steam App 1238020", "InstallLocation"),
            (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Wow6432Node\Microsoft\Windows\CurrentVersion\Uninstall\Steam App 1238020", "InstallLocation"),
        ]
        
        for hkey, subkey, value_name in registry_paths:
            try:
                with winreg.OpenKey(hkey, subkey) as key:
                    path, _ = winreg.QueryValueEx(key, value_name)
                    if path:
                        return str(Path(path).resolve())
            except (OSError, FileNotFoundError):
                continue
        
        return None
    
    def _detect_from_steam(self) -> Optional[str]:
        """Detect ME3 from Steam installation."""
        steam_paths = [
            Path.home() / ".steam" / "steam",  # Linux
            Path("C:") / "Program Files (x86)" / "Steam",  # Windows
            Path("C:") / "Program Files" / "Steam",  # Windows
        ]
        
        for steam_path in steam_paths:
            if not steam_path.exists():
                continue
            
            # Check common Steam library folders
            library_folders = [
                steam_path / "steamapps" / "common",
                steam_path / "steamapps" / "libraryfolders.vdf",
            ]
            
            # Look for ME3 in Steam libraries
            me3_folders = [
                "Mass Effect 3",
                "Mass Effect Legendary Edition",
            ]
            
            for library in library_folders:
                if library.name == "libraryfolders.vdf" and library.exists():
                    # Parse Steam library folders file
                    additional_paths = self._parse_steam_library_folders(library)
                    for additional_path in additional_paths:
                        for me3_folder in me3_folders:
                            me3_path = additional_path / "steamapps" / "common" / me3_folder
                            if me3_path.exists():
                                return str(me3_path)
                elif library.exists():
                    for me3_folder in me3_folders:
                        me3_path = library / me3_folder
                        if me3_path.exists():
                            return str(me3_path)
        
        return None
    
    def _parse_steam_library_folders(self, vdf_path: Path) -> List[Path]:
        """Parse Steam libraryfolders.vdf file."""
        try:
            with open(vdf_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            paths = []
            lines = content.split('\n')
            for line in lines:
                if '"path"' in line:
                    # Extract path from "path" "C:\\Steam"
                    parts = line.split('"')
                    if len(parts) >= 4:
                        path_str = parts[3].replace('\\\\', '\\')
                        paths.append(Path(path_str))
            
            return paths
        except Exception as e:
            self._logger.debug(f"Failed to parse Steam library folders: {e}")
            return []
    
    def _detect_from_origin(self) -> Optional[str]:
        """Detect ME3 from Origin/EA App."""
        origin_paths = [
            Path("C:") / "Program Files (x86)" / "Origin Games",
            Path("C:") / "Program Files" / "Origin Games",
            Path("C:") / "Program Files (x86)" / "EA Games",
            Path("C:") / "Program Files" / "EA Games",
        ]
        
        me3_folders = [
            "Mass Effect 3",
            "Mass Effect Legendary Edition",
        ]
        
        for origin_path in origin_paths:
            if not origin_path.exists():
                continue
            
            for me3_folder in me3_folders:
                me3_path = origin_path / me3_folder
                if me3_path.exists():
                    return str(me3_path)
        
        return None
    
    def _detect_from_common_paths(self) -> Optional[str]:
        """Detect ME3 from common installation paths."""
        common_paths = [
            # Windows common paths
            Path("C:") / "Games" / "Mass Effect 3",
            Path("C:") / "Games" / "Mass Effect Legendary Edition",
            Path("D:") / "Games" / "Mass Effect 3",
            Path("D:") / "Games" / "Mass Effect Legendary Edition",
            
            # User directories
            Path.home() / "Games" / "Mass Effect 3",
            Path.home() / "Games" / "Mass Effect Legendary Edition",
        ]
        
        for path in common_paths:
            if path.exists():
                return str(path)
        
        return None
    
    def _validate_me3_installation(self, path: str) -> bool:
        """Validate that the path contains a valid ME3 installation."""
        try:
            game_path = Path(path)
            
            # Check for essential ME3 files
            required_files = [
                "Binaries/Win32/MassEffect3.exe",
                "BIOGame/DLC",
                "BIOGame/CookedPC",
            ]
            
            for required_file in required_files:
                if not (game_path / required_file).exists():
                    self._logger.debug(f"Missing required file: {required_file}")
                    return False
            
            self._logger.info(f"Validated ME3 installation at: {path}")
            return True
            
        except Exception as e:
            self._logger.debug(f"Validation failed: {e}")
            return False
    
    def get_dlc_path(self, game_path: str) -> Optional[str]:
        """Get DLC directory path from game path."""
        try:
            dlc_path = Path(game_path) / "BIOGame" / "DLC"
            if dlc_path.exists():
                return str(dlc_path)
        except Exception as e:
            self._logger.error(f"Failed to get DLC path: {e}")
        
        return None
    
    def is_legendary_edition(self, game_path: str) -> bool:
        """Check if the installation is Legendary Edition."""
        try:
            # LE has different structure and files
            le_indicators = [
                "Game/ME1/BioGame",
                "Game/ME2/BioGame", 
                "Game/ME3/BIOGame",
                "Launcher/MassEffectLauncher.exe",
            ]
            
            game_path_obj = Path(game_path)
            for indicator in le_indicators:
                if (game_path_obj / indicator).exists():
                    return True
            
            return False
            
        except Exception:
            return False
