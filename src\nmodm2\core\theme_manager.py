"""
Theme management for NModM 2.0
"""

import logging
from typing import Dict, Optional, Any
from pathlib import Path

from PySide6.QtCore import QObject, Signal, Slot, Property
from PySide6.QtGui import QColor, QPalette
from PySide6.QtQml import QQmlApplicationEngine


class ThemeManager(QObject):
    """Theme management system."""
    
    # Signals
    themeChanged = Signal(str)  # theme name
    
    _instance: Optional['ThemeManager'] = None
    
    def __init__(self, parent: Optional[QObject] = None):
        super().__init__(parent)
        self._logger = logging.getLogger(__name__)
        self._current_theme = "dark"
        
        # Theme definitions
        self._themes = {
            "light": {
                "name": "Light",
                "primary": "#2196F3",
                "primaryDark": "#1976D2",
                "accent": "#FF4081",
                "background": "#FAFAFA",
                "surface": "#FFFFFF",
                "error": "#F44336",
                "onPrimary": "#FFFFFF",
                "onSecondary": "#000000",
                "onBackground": "#000000",
                "onSurface": "#000000",
                "onError": "#FFFFFF",
                "textPrimary": "#212121",
                "textSecondary": "#757575",
                "divider": "#BDBDBD",
                "disabled": "#9E9E9E",
                "cardBackground": "#FFFFFF",
                "dialogBackground": "#FFFFFF",
                "tooltipBackground": "#616161",
                "tooltipText": "#FFFFFF",
            },
            
            "dark": {
                "name": "Dark",
                "primary": "#BB86FC",
                "primaryDark": "#3700B3",
                "accent": "#03DAC6",
                "background": "#121212",
                "surface": "#1E1E1E",
                "error": "#CF6679",
                "onPrimary": "#000000",
                "onSecondary": "#FFFFFF",
                "onBackground": "#FFFFFF",
                "onSurface": "#FFFFFF",
                "onError": "#000000",
                "textPrimary": "#FFFFFF",
                "textSecondary": "#B3B3B3",
                "divider": "#373737",
                "disabled": "#6D6D6D",
                "cardBackground": "#2D2D2D",
                "dialogBackground": "#383838",
                "tooltipBackground": "#616161",
                "tooltipText": "#FFFFFF",
            },
            
            "me3": {
                "name": "Mass Effect 3",
                "primary": "#FF6B35",
                "primaryDark": "#E55A2B",
                "accent": "#00D4FF",
                "background": "#0A0A0A",
                "surface": "#1A1A1A",
                "error": "#FF4444",
                "onPrimary": "#FFFFFF",
                "onSecondary": "#FFFFFF",
                "onBackground": "#FFFFFF",
                "onSurface": "#FFFFFF",
                "onError": "#FFFFFF",
                "textPrimary": "#FFFFFF",
                "textSecondary": "#CCCCCC",
                "divider": "#444444",
                "disabled": "#666666",
                "cardBackground": "#252525",
                "dialogBackground": "#2A2A2A",
                "tooltipBackground": "#444444",
                "tooltipText": "#FFFFFF",
            }
        }
    
    @classmethod
    def instance(cls) -> 'ThemeManager':
        """Get singleton instance."""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    def apply_theme(self, theme_name: Optional[str] = None) -> None:
        """Apply a theme."""
        if theme_name is None:
            theme_name = self._current_theme
        
        if theme_name not in self._themes:
            self._logger.warning(f"Unknown theme: {theme_name}")
            theme_name = "dark"
        
        self._current_theme = theme_name
        self.themeChanged.emit(theme_name)
        self._logger.info(f"Applied theme: {theme_name}")
    
    def get_theme_colors(self, theme_name: Optional[str] = None) -> Dict[str, str]:
        """Get theme colors."""
        if theme_name is None:
            theme_name = self._current_theme
        
        return self._themes.get(theme_name, self._themes["dark"])
    
    def get_available_themes(self) -> Dict[str, str]:
        """Get available theme names and display names."""
        return {name: theme["name"] for name, theme in self._themes.items()}
    
    @Slot(str)
    def setTheme(self, theme_name: str) -> None:
        """QML-accessible theme setter."""
        self.apply_theme(theme_name)
    
    @Slot(result=str)
    def getCurrentTheme(self) -> str:
        """QML-accessible current theme getter."""
        return self._current_theme
    
    @Slot(result='QVariant')
    def getThemeColors(self) -> Dict[str, str]:
        """QML-accessible theme colors getter."""
        return self.get_theme_colors()
    
    @Slot(result='QVariant')
    def getAvailableThemes(self) -> Dict[str, str]:
        """QML-accessible available themes getter."""
        return self.get_available_themes()
    
    @Slot(str, result=str)
    def getColor(self, color_name: str) -> str:
        """Get a specific color from current theme."""
        colors = self.get_theme_colors()
        return colors.get(color_name, "#000000")
    
    # Properties for QML
    @Property(str, notify=themeChanged)
    def currentTheme(self) -> str:
        return self._current_theme
    
    @Property('QVariant', notify=themeChanged)
    def colors(self) -> Dict[str, str]:
        return self.get_theme_colors()
    
    @Property('QVariant', constant=True)
    def availableThemes(self) -> Dict[str, str]:
        return self.get_available_themes()
