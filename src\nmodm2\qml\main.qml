import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Controls.Material 2.15
import QtQuick.Window 2.15
import QtQuick.Layouts 1.15
import NModM 1.0

ApplicationWindow {
    id: mainWindow
    
    // Window properties
    width: Config.getValue("app.window_width") || 1200
    height: Config.getValue("app.window_height") || 800
    minimumWidth: 800
    minimumHeight: 600
    
    // Frameless window
    flags: Qt.Window | Qt.FramelessWindowHint
    
    // Material theme
    Material.theme: ThemeManager.currentTheme === "light" ? Material.Light : Material.Dark
    Material.primary: ThemeManager.getColor("primary")
    Material.accent: ThemeManager.getColor("accent")
    
    // Window title
    title: "NModM 2.0 - ME3 Mod Manager"
    
    // Background
    color: ThemeManager.getColor("background")
    
    // QML Bridge
    QMLBridge {
        id: qmlBridge
        
        onMessageReceived: function(type, data) {
            console.log("Message received:", type, data)
        }
        
        onErrorOccurred: function(component, error) {
            console.error("Error in", component + ":", error)
        }
    }
    
    // Main content
    ColumnLayout {
        anchors.fill: parent
        spacing: 0
        
        // Custom title bar
        Rectangle {
            id: titleBar
            Layout.fillWidth: true
            Layout.preferredHeight: 40
            color: ThemeManager.getColor("surface")
            
            // Title bar content
            RowLayout {
                anchors.fill: parent
                anchors.leftMargin: 16
                anchors.rightMargin: 8
                
                // App icon and title
                RowLayout {
                    Layout.fillWidth: true
                    spacing: 12
                    
                    Image {
                        source: "qrc:/resources/icon.png"
                        width: 24
                        height: 24
                        fillMode: Image.PreserveAspectFit
                        visible: false // Hide until we have an icon
                    }
                    
                    Text {
                        text: mainWindow.title
                        color: ThemeManager.getColor("textPrimary")
                        font.pixelSize: 14
                        font.weight: Font.Medium
                    }
                }
                
                // Window controls
                RowLayout {
                    spacing: 4
                    
                    // Minimize button
                    Button {
                        flat: true
                        width: 32
                        height: 32
                        text: "−"
                        font.pixelSize: 16
                        onClicked: qmlBridge.minimizeWindow()
                        
                        background: Rectangle {
                            color: parent.hovered ? ThemeManager.getColor("divider") : "transparent"
                            radius: 4
                        }
                    }
                    
                    // Maximize button
                    Button {
                        flat: true
                        width: 32
                        height: 32
                        text: qmlBridge.isMaximized ? "❐" : "□"
                        font.pixelSize: 12
                        onClicked: qmlBridge.maximizeWindow()
                        
                        background: Rectangle {
                            color: parent.hovered ? ThemeManager.getColor("divider") : "transparent"
                            radius: 4
                        }
                    }
                    
                    // Close button
                    Button {
                        flat: true
                        width: 32
                        height: 32
                        text: "×"
                        font.pixelSize: 18
                        onClicked: qmlBridge.closeWindow()
                        
                        background: Rectangle {
                            color: parent.hovered ? "#E81123" : "transparent"
                            radius: 4
                        }
                        
                        contentItem: Text {
                            text: parent.text
                            color: parent.hovered ? "white" : ThemeManager.getColor("textPrimary")
                            horizontalAlignment: Text.AlignHCenter
                            verticalAlignment: Text.AlignVCenter
                            font: parent.font
                        }
                    }
                }
            }
            
            // Drag area for window movement
            MouseArea {
                anchors.fill: parent
                anchors.rightMargin: 120 // Exclude window controls
                
                property point lastMousePos: Qt.point(0, 0)
                
                onPressed: function(mouse) {
                    lastMousePos = Qt.point(mouse.x, mouse.y)
                }
                
                onMouseXChanged: {
                    if (pressed) {
                        var dx = mouseX - lastMousePos.x
                        mainWindow.x += dx
                    }
                }
                
                onMouseYChanged: {
                    if (pressed) {
                        var dy = mouseY - lastMousePos.y
                        mainWindow.y += dy
                    }
                }
            }
        }
        
        // Main content area
        RowLayout {
            Layout.fillWidth: true
            Layout.fillHeight: true
            spacing: 0

            // Sidebar navigation
            Rectangle {
                id: sidebar
                Layout.preferredWidth: 250
                Layout.fillHeight: true
                color: ThemeManager.getColor("surface")

                ColumnLayout {
                    anchors.fill: parent
                    anchors.margins: 16
                    spacing: 8

                    // Logo and title
                    RowLayout {
                        Layout.fillWidth: true
                        spacing: 12

                        Rectangle {
                            width: 32
                            height: 32
                            radius: 16
                            color: ThemeManager.getColor("primary")

                            Text {
                                anchors.centerIn: parent
                                text: "N"
                                color: ThemeManager.getColor("onPrimary")
                                font.pixelSize: 18
                                font.weight: Font.Bold
                            }
                        }

                        Text {
                            text: "NModM 2.0"
                            color: ThemeManager.getColor("textPrimary")
                            font.pixelSize: 18
                            font.weight: Font.Medium
                        }
                    }

                    // Navigation buttons
                    NavigationButton {
                        id: homeButton
                        Layout.fillWidth: true
                        text: "Home"
                        icon: "🏠"
                        selected: stackView.currentItem === homeView
                        onClicked: stackView.replace(homeView)
                    }

                    NavigationButton {
                        id: modButton
                        Layout.fillWidth: true
                        text: "Mod Manager"
                        icon: "🎮"
                        selected: stackView.currentItem === modView
                        onClicked: stackView.replace(modView)
                    }

                    NavigationButton {
                        id: networkButton
                        Layout.fillWidth: true
                        text: "Network"
                        icon: "🌐"
                        selected: stackView.currentItem === networkView
                        onClicked: stackView.replace(networkView)
                    }

                    NavigationButton {
                        id: settingsButton
                        Layout.fillWidth: true
                        text: "Settings"
                        icon: "⚙️"
                        selected: stackView.currentItem === settingsView
                        onClicked: stackView.replace(settingsView)
                    }

                    // Spacer
                    Item {
                        Layout.fillHeight: true
                    }

                    // Status indicator
                    Rectangle {
                        Layout.fillWidth: true
                        height: 60
                        radius: 8
                        color: ThemeManager.getColor("cardBackground")
                        border.color: ThemeManager.getColor("divider")
                        border.width: 1

                        ColumnLayout {
                            anchors.fill: parent
                            anchors.margins: 12
                            spacing: 4

                            Text {
                                text: "Status"
                                color: ThemeManager.getColor("textSecondary")
                                font.pixelSize: 12
                            }

                            RowLayout {
                                spacing: 8

                                Rectangle {
                                    width: 8
                                    height: 8
                                    radius: 4
                                    color: networkManager.connected ? "#4CAF50" : "#F44336"
                                }

                                Text {
                                    text: networkManager.connected ? "Connected" : "Disconnected"
                                    color: ThemeManager.getColor("textPrimary")
                                    font.pixelSize: 14
                                }
                            }
                        }
                    }
                }
            }

            // Main content stack
            StackView {
                id: stackView
                Layout.fillWidth: true
                Layout.fillHeight: true

                initialItem: homeView

                // Home view
                Component {
                    id: homeView
                    HomeView {}
                }

                // Mod manager view
                Component {
                    id: modView
                    ModManagerView {}
                }

                // Network view
                Component {
                    id: networkView
                    NetworkView {}
                }

                // Settings view
                Component {
                    id: settingsView
                    SettingsView {}
                }
            }
        }
    }
    
    // Window state management
    onWidthChanged: Config.setValue("app.window_width", width)
    onHeightChanged: Config.setValue("app.window_height", height)
    
    Component.onCompleted: {
        // Apply saved theme
        var savedTheme = Config.getValue("app.theme") || "dark"
        ThemeManager.setTheme(savedTheme)
        
        console.log("NModM 2.0 initialized")
    }
}
