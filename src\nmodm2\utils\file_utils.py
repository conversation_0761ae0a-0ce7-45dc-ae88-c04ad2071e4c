"""
File utility functions
"""

import os
import shutil
import hashlib
import logging
from pathlib import Path
from typing import Optional, List, Dict, Any
import send2trash


class FileUtils:
    """File utility functions for mod management."""
    
    @staticmethod
    def safe_delete(file_path: str, use_trash: bool = True) -> bool:
        """Safely delete a file or directory."""
        try:
            path = Path(file_path)
            if not path.exists():
                return True
            
            if use_trash:
                send2trash.send2trash(str(path))
            else:
                if path.is_file():
                    path.unlink()
                elif path.is_dir():
                    shutil.rmtree(path)
            
            return True
            
        except Exception as e:
            logging.error(f"Failed to delete {file_path}: {e}")
            return False
    
    @staticmethod
    def safe_copy(src: str, dst: str, overwrite: bool = False) -> bool:
        """Safely copy a file or directory."""
        try:
            src_path = Path(src)
            dst_path = Path(dst)
            
            if not src_path.exists():
                return False
            
            if dst_path.exists() and not overwrite:
                return False
            
            # Create parent directories
            dst_path.parent.mkdir(parents=True, exist_ok=True)
            
            if src_path.is_file():
                shutil.copy2(src, dst)
            elif src_path.is_dir():
                if dst_path.exists():
                    shutil.rmtree(dst_path)
                shutil.copytree(src, dst)
            
            return True
            
        except Exception as e:
            logging.error(f"Failed to copy {src} to {dst}: {e}")
            return False
    
    @staticmethod
    def safe_move(src: str, dst: str, overwrite: bool = False) -> bool:
        """Safely move a file or directory."""
        try:
            src_path = Path(src)
            dst_path = Path(dst)
            
            if not src_path.exists():
                return False
            
            if dst_path.exists() and not overwrite:
                return False
            
            # Create parent directories
            dst_path.parent.mkdir(parents=True, exist_ok=True)
            
            if dst_path.exists():
                FileUtils.safe_delete(str(dst_path))
            
            shutil.move(src, dst)
            return True
            
        except Exception as e:
            logging.error(f"Failed to move {src} to {dst}: {e}")
            return False
    
    @staticmethod
    def calculate_checksum(file_path: str, algorithm: str = "md5") -> Optional[str]:
        """Calculate file checksum."""
        try:
            hash_func = hashlib.new(algorithm)
            
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_func.update(chunk)
            
            return hash_func.hexdigest()
            
        except Exception as e:
            logging.error(f"Failed to calculate checksum for {file_path}: {e}")
            return None
    
    @staticmethod
    def get_file_info(file_path: str) -> Dict[str, Any]:
        """Get detailed file information."""
        try:
            path = Path(file_path)
            if not path.exists():
                return {}
            
            stat = path.stat()
            
            return {
                'name': path.name,
                'size': stat.st_size,
                'modified': stat.st_mtime,
                'created': stat.st_ctime,
                'is_file': path.is_file(),
                'is_dir': path.is_dir(),
                'extension': path.suffix,
                'parent': str(path.parent),
            }
            
        except Exception as e:
            logging.error(f"Failed to get file info for {file_path}: {e}")
            return {}
    
    @staticmethod
    def find_files(directory: str, pattern: str = "*", recursive: bool = True) -> List[str]:
        """Find files matching a pattern."""
        try:
            dir_path = Path(directory)
            if not dir_path.exists():
                return []
            
            if recursive:
                files = dir_path.rglob(pattern)
            else:
                files = dir_path.glob(pattern)
            
            return [str(f) for f in files if f.is_file()]
            
        except Exception as e:
            logging.error(f"Failed to find files in {directory}: {e}")
            return []
    
    @staticmethod
    def create_backup(file_path: str, backup_dir: str = None) -> Optional[str]:
        """Create a backup of a file or directory."""
        try:
            src_path = Path(file_path)
            if not src_path.exists():
                return None
            
            if backup_dir is None:
                backup_dir = src_path.parent / "backups"
            
            backup_path = Path(backup_dir)
            backup_path.mkdir(parents=True, exist_ok=True)
            
            # Generate backup filename with timestamp
            import datetime
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"{src_path.name}.backup_{timestamp}"
            backup_file = backup_path / backup_name
            
            if FileUtils.safe_copy(str(src_path), str(backup_file)):
                return str(backup_file)
            
            return None
            
        except Exception as e:
            logging.error(f"Failed to create backup for {file_path}: {e}")
            return None
    
    @staticmethod
    def restore_backup(backup_path: str, target_path: str) -> bool:
        """Restore a file from backup."""
        try:
            if not Path(backup_path).exists():
                return False
            
            return FileUtils.safe_copy(backup_path, target_path, overwrite=True)
            
        except Exception as e:
            logging.error(f"Failed to restore backup {backup_path}: {e}")
            return False
    
    @staticmethod
    def ensure_directory(directory: str) -> bool:
        """Ensure a directory exists."""
        try:
            Path(directory).mkdir(parents=True, exist_ok=True)
            return True
        except Exception as e:
            logging.error(f"Failed to create directory {directory}: {e}")
            return False
    
    @staticmethod
    def is_writable(path: str) -> bool:
        """Check if a path is writable."""
        try:
            path_obj = Path(path)
            if path_obj.exists():
                return os.access(path, os.W_OK)
            else:
                # Check parent directory
                return os.access(path_obj.parent, os.W_OK)
        except Exception:
            return False
    
    @staticmethod
    def format_size(size_bytes: int) -> str:
        """Format size in human readable format."""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        size = float(size_bytes)
        
        while size >= 1024.0 and i < len(size_names) - 1:
            size /= 1024.0
            i += 1
        
        return f"{size:.1f} {size_names[i]}"
