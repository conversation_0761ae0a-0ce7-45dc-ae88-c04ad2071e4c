import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Controls.Material 2.15
import QtQuick.Layouts 1.15
import NModM 1.0

ColumnLayout {
    id: root
    spacing: 16
    
    // Header
    RowLayout {
        Layout.fillWidth: true
        spacing: 16
        
        Text {
            text: "EasyTier Network"
            color: ThemeManager.getColor("textPrimary")
            font.pixelSize: 24
            font.weight: Font.Bold
            Layout.fillWidth: true
        }
        
        Button {
            text: networkManager.connected ? "Disconnect" : "Connect"
            highlighted: !networkManager.connected
            onClicked: {
                if (networkManager.connected) {
                    networkManager.disconnect()
                } else {
                    connectDialog.open()
                }
            }
            enabled: networkManager.binaryAvailable
        }
        
        Button {
            text: "Settings"
            onClicked: networkSettingsDialog.open()
        }
    }
    
    // Status section
    Rectangle {
        Layout.fillWidth: true
        height: 120
        color: ThemeManager.getColor("surface")
        radius: 8
        
        ColumnLayout {
            anchors.fill: parent
            anchors.margins: 16
            spacing: 12
            
            RowLayout {
                Layout.fillWidth: true
                spacing: 16
                
                RowLayout {
                    spacing: 8
                    
                    Rectangle {
                        width: 16
                        height: 16
                        radius: 8
                        color: networkManager.connected ? "#4CAF50" : "#F44336"
                    }
                    
                    Text {
                        text: networkManager.connected ? "Connected" : "Disconnected"
                        color: ThemeManager.getColor("textPrimary")
                        font.pixelSize: 16
                        font.weight: Font.Medium
                    }
                }
                
                Item { Layout.fillWidth: true }
                
                Text {
                    text: networkManager.nodeCount + " nodes"
                    color: ThemeManager.getColor("textSecondary")
                    font.pixelSize: 14
                }
            }
            
            GridLayout {
                Layout.fillWidth: true
                columns: 2
                columnSpacing: 24
                rowSpacing: 8
                
                Text {
                    text: "Network:"
                    color: ThemeManager.getColor("textSecondary")
                    font.pixelSize: 12
                }
                Text {
                    text: networkManager.networkName || "Not connected"
                    color: ThemeManager.getColor("textPrimary")
                    font.pixelSize: 12
                }
                
                Text {
                    text: "Local IP:"
                    color: ThemeManager.getColor("textSecondary")
                    font.pixelSize: 12
                }
                Text {
                    text: networkManager.getIPv4Address() || "Not assigned"
                    color: ThemeManager.getColor("textPrimary")
                    font.pixelSize: 12
                }
                
                Text {
                    text: "Binary:"
                    color: ThemeManager.getColor("textSecondary")
                    font.pixelSize: 12
                }
                Text {
                    text: networkManager.binaryAvailable ? "Available" : "Not found"
                    color: networkManager.binaryAvailable ? "#4CAF50" : "#F44336"
                    font.pixelSize: 12
                }
            }
        }
    }
    
    // Node list
    Rectangle {
        Layout.fillWidth: true
        Layout.fillHeight: true
        color: ThemeManager.getColor("surface")
        radius: 8
        
        ColumnLayout {
            anchors.fill: parent
            anchors.margins: 16
            spacing: 12
            
            RowLayout {
                Layout.fillWidth: true
                spacing: 16
                
                Text {
                    text: "Connected Nodes"
                    color: ThemeManager.getColor("textPrimary")
                    font.pixelSize: 16
                    font.weight: Font.Medium
                    Layout.fillWidth: true
                }
                
                Button {
                    text: "Refresh"
                    onClicked: networkManager.refreshNodes()
                    enabled: networkManager.connected
                }
            }
            
            ScrollView {
                Layout.fillWidth: true
                Layout.fillHeight: true
                
                ListView {
                    id: nodeListView
                    model: networkManager.getNodeList()
                    spacing: 8
                    
                    delegate: Rectangle {
                        width: nodeListView.width
                        height: 100
                        color: ThemeManager.getColor("cardBackground")
                        radius: 8
                        border.color: ThemeManager.getColor("divider")
                        border.width: 1
                        
                        RowLayout {
                            anchors.fill: parent
                            anchors.margins: 16
                            spacing: 16
                            
                            // Node status indicator
                            Rectangle {
                                width: 12
                                height: 12
                                radius: 6
                                color: modelData.cost === "Local" ? "#2196F3" : 
                                       modelData.cost === "p2p" ? "#4CAF50" : "#FF9800"
                            }
                            
                            // Node info
                            ColumnLayout {
                                Layout.fillWidth: true
                                spacing: 4
                                
                                RowLayout {
                                    spacing: 12
                                    
                                    Text {
                                        text: modelData.hostname || "Unknown"
                                        color: ThemeManager.getColor("textPrimary")
                                        font.pixelSize: 16
                                        font.weight: Font.Medium
                                    }
                                    
                                    Text {
                                        text: modelData.ipv4 || "No IP"
                                        color: ThemeManager.getColor("textSecondary")
                                        font.pixelSize: 12
                                        font.family: "monospace"
                                    }
                                }
                                
                                RowLayout {
                                    spacing: 16
                                    
                                    Text {
                                        text: "Latency: " + (modelData.latency_ms ? modelData.latency_ms + "ms" : "N/A")
                                        color: ThemeManager.getColor("textSecondary")
                                        font.pixelSize: 12
                                    }
                                    
                                    Text {
                                        text: "Protocol: " + (modelData.tunnel_proto || "Unknown")
                                        color: ThemeManager.getColor("textSecondary")
                                        font.pixelSize: 12
                                    }
                                    
                                    Text {
                                        text: "NAT: " + (modelData.nat_type || "Unknown")
                                        color: ThemeManager.getColor("textSecondary")
                                        font.pixelSize: 12
                                    }
                                }
                                
                                RowLayout {
                                    spacing: 16
                                    
                                    Text {
                                        text: "RX: " + (modelData.rx_bytes_formatted || "0 B")
                                        color: ThemeManager.getColor("textSecondary")
                                        font.pixelSize: 12
                                    }
                                    
                                    Text {
                                        text: "TX: " + (modelData.tx_bytes_formatted || "0 B")
                                        color: ThemeManager.getColor("textSecondary")
                                        font.pixelSize: 12
                                    }
                                    
                                    Text {
                                        text: "Version: " + (modelData.version || "Unknown")
                                        color: ThemeManager.getColor("textSecondary")
                                        font.pixelSize: 12
                                    }
                                }
                            }
                            
                            // Connection type badge
                            Rectangle {
                                width: 60
                                height: 24
                                radius: 12
                                color: modelData.cost === "Local" ? "#2196F3" : 
                                       modelData.cost === "p2p" ? "#4CAF50" : "#FF9800"
                                
                                Text {
                                    anchors.centerIn: parent
                                    text: modelData.cost || "Unknown"
                                    color: "white"
                                    font.pixelSize: 10
                                    font.weight: Font.Medium
                                }
                            }
                        }
                    }
                    
                    // Empty state
                    Label {
                        anchors.centerIn: parent
                        text: networkManager.connected ? 
                              "No nodes connected\nClick 'Refresh' to update" : 
                              "Not connected to network\nClick 'Connect' to join a network"
                        color: ThemeManager.getColor("textSecondary")
                        font.pixelSize: 14
                        horizontalAlignment: Text.AlignHCenter
                        visible: nodeListView.count === 0
                    }
                }
            }
        }
    }
    
    // Connect dialog
    Dialog {
        id: connectDialog
        title: "Connect to Network"
        modal: true
        anchors.centerIn: parent
        width: 400
        height: 350
        
        ColumnLayout {
            anchors.fill: parent
            spacing: 16
            
            Text {
                text: "Network Configuration"
                color: ThemeManager.getColor("textPrimary")
                font.pixelSize: 16
                font.weight: Font.Medium
            }
            
            GridLayout {
                Layout.fillWidth: true
                columns: 2
                columnSpacing: 12
                rowSpacing: 12
                
                Text { text: "Network Name:"; color: ThemeManager.getColor("textSecondary") }
                TextField {
                    id: networkNameField
                    text: networkManager.getNetworkName()
                    placeholderText: "Enter network name..."
                    Layout.fillWidth: true
                }
                
                Text { text: "Network Secret:"; color: ThemeManager.getColor("textSecondary") }
                TextField {
                    id: networkSecretField
                    text: networkManager.getNetworkSecret()
                    placeholderText: "Enter network secret..."
                    echoMode: TextInput.Password
                    Layout.fillWidth: true
                }
                
                Text { text: "IPv4 Address:"; color: ThemeManager.getColor("textSecondary") }
                TextField {
                    id: ipv4Field
                    text: networkManager.getIPv4Address()
                    placeholderText: "Auto-assign (optional)"
                    Layout.fillWidth: true
                }
            }
            
            Text {
                text: "Peers (one per line):"
                color: ThemeManager.getColor("textSecondary")
            }
            
            ScrollView {
                Layout.fillWidth: true
                Layout.preferredHeight: 100
                
                TextArea {
                    id: peersField
                    text: networkManager.getPeers().join('\n')
                    placeholderText: "tcp://public.easytier.cn:11010\nudp://peer.example.com:11010"
                    wrapMode: TextArea.Wrap
                }
            }
            
            RowLayout {
                Layout.fillWidth: true
                
                Item { Layout.fillWidth: true }
                
                Button {
                    text: "Cancel"
                    onClicked: connectDialog.close()
                }
                
                Button {
                    text: "Connect"
                    highlighted: true
                    enabled: networkNameField.text && networkSecretField.text
                    onClicked: {
                        networkManager.setNetworkName(networkNameField.text)
                        networkManager.setNetworkSecret(networkSecretField.text)
                        if (ipv4Field.text) {
                            networkManager.setIPv4Address(ipv4Field.text)
                        }
                        
                        var peers = peersField.text.split('\n').filter(function(peer) {
                            return peer.trim().length > 0
                        })
                        networkManager.setPeers(peers)
                        
                        networkManager.connect(networkNameField.text, networkSecretField.text, peers)
                        connectDialog.close()
                    }
                }
            }
        }
    }
    
    // Network settings dialog
    Dialog {
        id: networkSettingsDialog
        title: "Network Settings"
        modal: true
        anchors.centerIn: parent
        width: 400
        height: 300
        
        ColumnLayout {
            anchors.fill: parent
            spacing: 16
            
            Text {
                text: "EasyTier Binary Path"
                color: ThemeManager.getColor("textPrimary")
                font.weight: Font.Medium
            }
            
            RowLayout {
                Layout.fillWidth: true
                
                TextField {
                    id: binaryPathField
                    text: networkManager.getBinaryPath()
                    placeholderText: "Path to easytier-core..."
                    Layout.fillWidth: true
                }
                
                Button {
                    text: "Browse"
                    onClicked: {
                        // TODO: Open file dialog
                    }
                }
            }
            
            Text {
                text: "Listen Port"
                color: ThemeManager.getColor("textPrimary")
                font.weight: Font.Medium
            }
            
            SpinBox {
                id: portField
                from: 1024
                to: 65535
                value: networkManager.getListenPort()
                Layout.fillWidth: true
            }
            
            Item { Layout.fillHeight: true }
            
            RowLayout {
                Layout.fillWidth: true
                
                Item { Layout.fillWidth: true }
                
                Button {
                    text: "Cancel"
                    onClicked: networkSettingsDialog.close()
                }
                
                Button {
                    text: "Apply"
                    onClicked: {
                        if (binaryPathField.text) {
                            networkManager.setBinaryPath(binaryPathField.text)
                        }
                        networkManager.setListenPort(portField.value)
                        networkSettingsDialog.close()
                    }
                }
            }
        }
    }
}
