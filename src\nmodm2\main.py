#!/usr/bin/env python3
"""
NModM 2.0 - Main application entry point
"""

import sys
import os
import logging
from pathlib import Path
from typing import Optional

from PySide6.QtCore import QCoreApplication, Qt
from PySide6.QtGui import QGuiApplication, QIcon
from PySide6.QtQml import QQmlApplication<PERSON>ngine, qmlRegisterSingletonType, qmlRegisterType
from PySide6.QtQuickControls2 import QQuickStyle

from .core.application import Application
from .core.config import Config
from .core.theme_manager import ThemeManager
from .core.qml_bridge import QMLBridge
from .core.i18n_manager import I18nManager
from .me3.mod_manager import ModManager
from .easytier.network_manager import NetworkManager


def setup_logging() -> None:
    """Setup application logging."""
    log_dir = Path.home() / ".nmodm2" / "logs"
    log_dir.mkdir(parents=True, exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_dir / "nmodm2.log"),
            logging.StreamHandler(sys.stdout)
        ]
    )


def register_qml_types() -> None:
    """Register custom QML types."""
    # Register singleton types with proper callback signature
    def config_provider(engine):
        return Config.instance()

    def theme_provider(engine):
        return ThemeManager.instance()

    def i18n_provider(engine):
        return I18nManager.instance()

    qmlRegisterSingletonType(
        Config, "NModM", 1, 0, "Config", config_provider
    )
    qmlRegisterSingletonType(
        ThemeManager, "NModM", 1, 0, "ThemeManager", theme_provider
    )
    qmlRegisterSingletonType(
        I18nManager, "NModM", 1, 0, "I18n", i18n_provider
    )

    # Register regular types
    qmlRegisterType(QMLBridge, "NModM", 1, 0, "QMLBridge")
    qmlRegisterType(ModManager, "NModM", 1, 0, "ModManager")
    qmlRegisterType(NetworkManager, "NModM", 1, 0, "NetworkManager")


def setup_qml_engine(app: QGuiApplication) -> QQmlApplicationEngine:
    """Setup and configure QML engine."""
    engine = QQmlApplicationEngine()
    
    # Set application properties
    engine.rootContext().setContextProperty("applicationVersion", "2.0.0")
    engine.rootContext().setContextProperty("applicationName", "NModM 2.0")
    
    # Add import paths
    current_dir = Path(__file__).parent
    qml_dir = current_dir / "qml"
    engine.addImportPath(str(qml_dir))
    
    # Load main QML file
    main_qml = qml_dir / "main.qml"
    engine.load(str(main_qml))
    
    if not engine.rootObjects():
        raise RuntimeError("Failed to load QML file")
    
    return engine


def main() -> int:
    """Main application entry point."""
    # Enable high DPI scaling
    QCoreApplication.setAttribute(Qt.AA_EnableHighDpiScaling)
    QCoreApplication.setAttribute(Qt.AA_UseHighDpiPixmaps)
    
    # Create application
    app = QGuiApplication(sys.argv)
    app.setApplicationName("NModM 2.0")
    app.setApplicationVersion("2.0.0")
    app.setOrganizationName("NModM")
    app.setOrganizationDomain("nmodm.local")
    
    # Setup logging
    setup_logging()
    logger = logging.getLogger(__name__)
    logger.info("Starting NModM 2.0...")
    
    try:
        # Set application icon
        icon_path = Path(__file__).parent / "resources" / "icon.png"
        if icon_path.exists():
            app.setWindowIcon(QIcon(str(icon_path)))
        
        # Set Material style
        QQuickStyle.setStyle("Material")
        
        # Initialize core components
        config = Config.instance()
        theme_manager = ThemeManager.instance()
        i18n_manager = I18nManager.instance()

        # Set initial language from config
        saved_language = config.get("app.language", "en")
        i18n_manager.set_language(saved_language)
        
        # Register QML types
        register_qml_types()
        
        # Setup QML engine
        engine = setup_qml_engine(app)
        
        # Initialize application
        application = Application(app, engine, config, theme_manager, i18n_manager)
        application.initialize()
        
        logger.info("Application initialized successfully")
        
        # Run application
        return app.exec()
        
    except Exception as e:
        logger.error(f"Application failed to start: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
