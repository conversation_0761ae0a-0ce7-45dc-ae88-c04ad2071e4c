[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "nmodm2"
version = "2.0.0"
description = "ME3 Mod Manager 2.0 with EasyTier Network Support"
authors = [
    {name = "Your Name", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.9"
dependencies = [
    "PySide6>=6.6.0",
    "aiohttp>=3.9.0",
    "psutil>=5.9.0",
    "watchdog>=3.0.0",
    "pyyaml>=6.0.1",
    "requests>=2.31.0",
    "typing-extensions>=4.8.0",
    "send2trash>=1.8.2",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
    "nuitka>=1.8.0",
]

[project.scripts]
nmodm2 = "nmodm2.main:main"

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-data]
"nmodm2.qml" = ["*.qml", "*.js"]
"nmodm2.resources" = ["*.png", "*.svg", "*.ico", "*.qm"]

[tool.black]
line-length = 88
target-version = ['py39']

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
asyncio_mode = "auto"
