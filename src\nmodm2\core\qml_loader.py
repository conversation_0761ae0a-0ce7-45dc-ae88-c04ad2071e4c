"""
QML Component Loader with lazy loading and caching
"""

import logging
from pathlib import Path
from typing import Dict, Optional, Any
from weakref import WeakValueDictionary

from PySide6.QtCore import QObject, Signal, Slot, QUrl, QTimer
from PySide6.QtQml import QQmlComponent, QQmlApplicationEngine


class QMLLoader(QObject):
    """QML component loader with lazy loading and caching."""
    
    # Signals
    componentLoaded = Signal(str, QObject)  # component_id, component
    componentError = Signal(str, str)  # component_id, error
    
    def __init__(self, engine: QQmlApplicationEngine, parent: Optional[QObject] = None):
        super().__init__(parent)
        self._logger = logging.getLogger(__name__)
        self._engine = engine
        
        # Component cache (weak references to allow garbage collection)
        self._component_cache: WeakValueDictionary = WeakValueDictionary()
        self._loading_components: Dict[str, QQmlComponent] = {}
        
        # Preload timer for background loading
        self._preload_timer = QTimer()
        self._preload_timer.timeout.connect(self._preload_next_component)
        self._preload_timer.setSingleShot(True)
        
        # Preload queue
        self._preload_queue: list = []
        self._preload_index = 0
    
    def load_component(self, component_id: str, qml_path: str, preload: bool = False) -> Optional[QObject]:
        """Load a QML component with caching."""
        try:
            # Check cache first
            if component_id in self._component_cache:
                cached_component = self._component_cache[component_id]
                if cached_component:
                    self._logger.debug(f"Using cached component: {component_id}")
                    return cached_component
            
            # Check if already loading
            if component_id in self._loading_components:
                self._logger.debug(f"Component already loading: {component_id}")
                return None
            
            # Create component
            qml_url = QUrl.fromLocalFile(str(Path(qml_path).resolve()))
            component = QQmlComponent(self._engine, qml_url)
            
            if preload:
                # Asynchronous loading for preload
                self._loading_components[component_id] = component
                component.statusChanged.connect(
                    lambda status, cid=component_id: self._on_component_status_changed(cid, status)
                )
                return None
            else:
                # Synchronous loading for immediate use
                if component.status() == QQmlComponent.Ready:
                    obj = component.create()
                    if obj:
                        self._component_cache[component_id] = obj
                        self.componentLoaded.emit(component_id, obj)
                        return obj
                    else:
                        error_msg = f"Failed to create component: {component.errorString()}"
                        self._logger.error(error_msg)
                        self.componentError.emit(component_id, error_msg)
                        return None
                else:
                    error_msg = f"Component not ready: {component.errorString()}"
                    self._logger.error(error_msg)
                    self.componentError.emit(component_id, error_msg)
                    return None
                    
        except Exception as e:
            error_msg = f"Failed to load component {component_id}: {e}"
            self._logger.error(error_msg)
            self.componentError.emit(component_id, error_msg)
            return None
    
    def _on_component_status_changed(self, component_id: str, status: QQmlComponent.Status) -> None:
        """Handle component status changes for async loading."""
        try:
            component = self._loading_components.get(component_id)
            if not component:
                return
            
            if status == QQmlComponent.Ready:
                obj = component.create()
                if obj:
                    self._component_cache[component_id] = obj
                    self.componentLoaded.emit(component_id, obj)
                    self._logger.debug(f"Component loaded asynchronously: {component_id}")
                else:
                    error_msg = f"Failed to create component: {component.errorString()}"
                    self._logger.error(error_msg)
                    self.componentError.emit(component_id, error_msg)
                
                # Remove from loading list
                del self._loading_components[component_id]
                
            elif status == QQmlComponent.Error:
                error_msg = f"Component loading error: {component.errorString()}"
                self._logger.error(error_msg)
                self.componentError.emit(component_id, error_msg)
                
                # Remove from loading list
                del self._loading_components[component_id]
                
        except Exception as e:
            self._logger.error(f"Error handling component status change: {e}")
    
    def preload_components(self, components: list, delay_ms: int = 100) -> None:
        """Preload components in background with delay between loads."""
        self._preload_queue = components.copy()
        self._preload_index = 0
        
        if self._preload_queue:
            self._preload_timer.start(delay_ms)
    
    def _preload_next_component(self) -> None:
        """Preload next component in queue."""
        if self._preload_index < len(self._preload_queue):
            component_info = self._preload_queue[self._preload_index]
            component_id = component_info.get('id')
            qml_path = component_info.get('path')
            
            if component_id and qml_path:
                self._logger.debug(f"Preloading component: {component_id}")
                self.load_component(component_id, qml_path, preload=True)
            
            self._preload_index += 1
            
            # Schedule next preload
            if self._preload_index < len(self._preload_queue):
                self._preload_timer.start(100)  # 100ms delay between preloads
    
    def get_cached_component(self, component_id: str) -> Optional[QObject]:
        """Get cached component without loading."""
        return self._component_cache.get(component_id)
    
    def clear_cache(self) -> None:
        """Clear component cache."""
        self._component_cache.clear()
        self._logger.info("Component cache cleared")
    
    def get_cache_info(self) -> Dict[str, Any]:
        """Get cache information for debugging."""
        return {
            'cached_components': len(self._component_cache),
            'loading_components': len(self._loading_components),
            'preload_queue_size': len(self._preload_queue),
            'preload_progress': self._preload_index
        }
    
    # QML-accessible methods
    @Slot(str, str, result=QObject)
    def loadComponent(self, component_id: str, qml_path: str) -> Optional[QObject]:
        """QML-accessible load component method."""
        return self.load_component(component_id, qml_path)
    
    @Slot(str, result=QObject)
    def getCachedComponent(self, component_id: str) -> Optional[QObject]:
        """QML-accessible get cached component method."""
        return self.get_cached_component(component_id)
    
    @Slot()
    def clearCache(self) -> None:
        """QML-accessible clear cache method."""
        self.clear_cache()
    
    @Slot(result='QVariant')
    def getCacheInfo(self) -> Dict[str, Any]:
        """QML-accessible get cache info method."""
        return self.get_cache_info()


class ComponentPreloader:
    """Helper class for defining component preload configurations."""
    
    @staticmethod
    def get_default_preload_config() -> list:
        """Get default component preload configuration."""
        return [
            {
                'id': 'home_view',
                'path': 'qml/HomeView.qml',
                'priority': 1
            },
            {
                'id': 'mod_manager_view',
                'path': 'qml/ModManagerView.qml',
                'priority': 2
            },
            {
                'id': 'network_view',
                'path': 'qml/NetworkView.qml',
                'priority': 2
            },
            {
                'id': 'settings_view',
                'path': 'qml/SettingsView.qml',
                'priority': 3
            },
            {
                'id': 'navigation_button',
                'path': 'qml/NavigationButton.qml',
                'priority': 1
            }
        ]
    
    @staticmethod
    def sort_by_priority(components: list) -> list:
        """Sort components by priority (lower number = higher priority)."""
        return sorted(components, key=lambda x: x.get('priority', 999))
