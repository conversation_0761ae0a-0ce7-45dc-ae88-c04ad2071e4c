"""
ME3 Mod Manager - Complete implementation
"""

import logging
import os
import sys
from pathlib import Path
from typing import Dict, List, Optional, Any
import json

from PySide6.QtCore import QObject, Signal, Slot, QTimer, Property
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

from .game_detector import GameDetector
from .mod_scanner import ModScanner
from .mod_model import ModInfo, ModStatus
from ..utils.file_utils import FileUtils


class ModFileWatcher(FileSystemEventHandler):
    """File system watcher for mod directory changes."""

    def __init__(self, mod_manager):
        super().__init__()
        self.mod_manager = mod_manager

    def on_created(self, event):
        if not event.is_directory:
            self.mod_manager.on_file_changed(event.src_path, "created")

    def on_deleted(self, event):
        if not event.is_directory:
            self.mod_manager.on_file_changed(event.src_path, "deleted")

    def on_modified(self, event):
        if not event.is_directory:
            self.mod_manager.on_file_changed(event.src_path, "modified")


class ModManager(QObject):
    """ME3 Mod Manager - Complete implementation."""

    # Signals
    statusChanged = Signal(str)
    modListChanged = Signal()
    gamePathChanged = Signal(str)
    modStatusChanged = Signal(str, str)  # mod_name, status
    scanProgress = Signal(int, int)  # current, total
    errorOccurred = Signal(str, str)  # component, error

    def __init__(self, parent: Optional[QObject] = None):
        super().__init__(parent)
        self._logger = logging.getLogger(__name__)
        self._initialized = False

        # Game detection
        self._game_detector = GameDetector()
        self._game_path = ""
        self._dlc_path = ""

        # Mod management
        self._mod_scanner = ModScanner()
        self._mods: Dict[str, ModInfo] = {}
        self._enabled_mods: List[str] = []

        # File watching
        self._observer: Optional[Observer] = None
        self._file_watcher = ModFileWatcher(self)

        # Status tracking
        self._scan_timer = QTimer()
        self._scan_timer.timeout.connect(self._periodic_scan)
        self._scan_timer.setSingleShot(False)

        # Configuration
        self._auto_detect = True
        self._backup_enabled = True
        self._backup_path = ""
        self._scan_interval = 30  # seconds

    def initialize(self) -> None:
        """Initialize the mod manager."""
        try:
            self._logger.info("Initializing ME3 Mod Manager...")

            # Detect game installation
            if self._auto_detect:
                self._detect_game_path()

            # Initialize mod scanner
            if self._game_path:
                self._mod_scanner.set_game_path(self._game_path)
                self._dlc_path = str(Path(self._game_path) / "BIOGame" / "DLC")

                # Start file watching
                self._start_file_watching()

                # Initial mod scan
                self._scan_mods()

                # Start periodic scanning
                if self._scan_interval > 0:
                    self._scan_timer.start(self._scan_interval * 1000)

            self._initialized = True
            self.statusChanged.emit("Initialized")
            self._logger.info("ME3 Mod Manager initialized successfully")

        except Exception as e:
            self._logger.error(f"Failed to initialize ME3 Mod Manager: {e}")
            self.errorOccurred.emit("ModManager", str(e))
            raise

    def _detect_game_path(self) -> None:
        """Detect ME3 game installation path."""
        try:
            game_path = self._game_detector.detect_me3_path()
            if game_path:
                self._game_path = game_path
                self.gamePathChanged.emit(game_path)
                self._logger.info(f"Detected ME3 at: {game_path}")
            else:
                self._logger.warning("ME3 installation not found")
                self.statusChanged.emit("Game not found")
        except Exception as e:
            self._logger.error(f"Game detection failed: {e}")
            self.errorOccurred.emit("GameDetection", str(e))

    def _start_file_watching(self) -> None:
        """Start watching DLC directory for changes."""
        if self._dlc_path and Path(self._dlc_path).exists():
            try:
                self._observer = Observer()
                self._observer.schedule(self._file_watcher, self._dlc_path, recursive=True)
                self._observer.start()
                self._logger.info(f"Started watching: {self._dlc_path}")
            except Exception as e:
                self._logger.warning(f"Failed to start file watching: {e}")

    def _scan_mods(self) -> None:
        """Scan for installed mods."""
        if not self._dlc_path:
            return

        try:
            self._logger.info("Scanning for mods...")
            mods = self._mod_scanner.scan_mods(self._dlc_path)

            # Update mod list
            old_count = len(self._mods)
            self._mods = {mod.name: mod for mod in mods}
            new_count = len(self._mods)

            # Emit signals
            if old_count != new_count:
                self.modListChanged.emit()

            self.scanProgress.emit(new_count, new_count)
            self._logger.info(f"Found {new_count} mods")

        except Exception as e:
            self._logger.error(f"Mod scanning failed: {e}")
            self.errorOccurred.emit("ModScanning", str(e))

    def _periodic_scan(self) -> None:
        """Periodic mod scanning."""
        self._scan_mods()

    def on_file_changed(self, file_path: str, event_type: str) -> None:
        """Handle file system changes."""
        self._logger.debug(f"File {event_type}: {file_path}")
        # Trigger rescan after a short delay
        QTimer.singleShot(1000, self._scan_mods)

    def update_status(self) -> None:
        """Update mod manager status."""
        if not self._initialized:
            return

        try:
            if self._game_path:
                enabled_count = len(self._enabled_mods)
                total_count = len(self._mods)
                status = f"Ready - {enabled_count}/{total_count} mods enabled"
            else:
                status = "Game not found"

            self.statusChanged.emit(status)

        except Exception as e:
            self._logger.warning(f"Status update failed: {e}")

    def cleanup(self) -> None:
        """Cleanup mod manager."""
        self._logger.info("Cleaning up ME3 Mod Manager...")

        # Stop timers
        self._scan_timer.stop()

        # Stop file watching
        if self._observer:
            self._observer.stop()
            self._observer.join()
            self._observer = None

        self._initialized = False

    # QML-accessible methods
    @Slot(result=bool)
    def isInitialized(self) -> bool:
        """Check if mod manager is initialized."""
        return self._initialized

    @Slot(result=str)
    def getGamePath(self) -> str:
        """Get current game path."""
        return self._game_path

    @Slot(str)
    def setGamePath(self, path: str) -> None:
        """Set game path manually."""
        if Path(path).exists():
            self._game_path = path
            self._dlc_path = str(Path(path) / "BIOGame" / "DLC")
            self.gamePathChanged.emit(path)

            # Reinitialize with new path
            if self._initialized:
                self.cleanup()
                self.initialize()

    @Slot(result='QVariant')
    def getModList(self) -> List[Dict[str, Any]]:
        """Get list of all mods."""
        return [mod.to_dict() for mod in self._mods.values()]

    @Slot(str, result=bool)
    def isModEnabled(self, mod_name: str) -> bool:
        """Check if a mod is enabled."""
        return mod_name in self._enabled_mods

    @Slot(str, bool)
    def setModEnabled(self, mod_name: str, enabled: bool) -> None:
        """Enable or disable a mod."""
        try:
            if mod_name not in self._mods:
                self._logger.warning(f"Mod not found: {mod_name}")
                return

            mod = self._mods[mod_name]

            if enabled and mod_name not in self._enabled_mods:
                self._enabled_mods.append(mod_name)
                mod.status = ModStatus.ENABLED
                self._logger.info(f"Enabled mod: {mod_name}")
            elif not enabled and mod_name in self._enabled_mods:
                self._enabled_mods.remove(mod_name)
                mod.status = ModStatus.DISABLED
                self._logger.info(f"Disabled mod: {mod_name}")

            self.modStatusChanged.emit(mod_name, mod.status.value)

        except Exception as e:
            self._logger.error(f"Failed to set mod status: {e}")
            self.errorOccurred.emit("ModStatus", str(e))

    @Slot()
    def refreshMods(self) -> None:
        """Manually refresh mod list."""
        self._scan_mods()

    @Slot(result=int)
    def getModCount(self) -> int:
        """Get total number of mods."""
        return len(self._mods)

    @Slot(result=int)
    def getEnabledModCount(self) -> int:
        """Get number of enabled mods."""
        return len(self._enabled_mods)

    # Properties for QML
    @Property(str, notify=gamePathChanged)
    def gamePath(self) -> str:
        return self._game_path

    @Property(int, notify=modListChanged)
    def modCount(self) -> int:
        return len(self._mods)

    @Property(int, notify=modListChanged)
    def enabledModCount(self) -> int:
        return len(self._enabled_mods)
