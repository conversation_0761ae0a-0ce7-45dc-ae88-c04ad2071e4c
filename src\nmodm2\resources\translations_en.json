{"app": {"name": "NModM 2.0", "description": "ME3 Mod Manager with EasyTier Network Support", "version": "Version {0}"}, "navigation": {"home": "Home", "mod_manager": "Mod Manager", "network": "Network", "settings": "Settings"}, "common": {"ok": "OK", "cancel": "Cancel", "apply": "Apply", "close": "Close", "save": "Save", "delete": "Delete", "edit": "Edit", "add": "Add", "remove": "Remove", "browse": "Browse", "refresh": "Refresh", "connect": "Connect", "disconnect": "Disconnect", "enable": "Enable", "disable": "Disable", "enabled": "Enabled", "disabled": "Disabled", "connected": "Connected", "disconnected": "Disconnected", "loading": "Loading...", "error": "Error", "warning": "Warning", "info": "Information", "success": "Success"}, "home": {"title": "Home", "quick_stats": "Quick Stats", "quick_actions": "Quick Actions", "me3_mods": "ME3 Mods", "network": "Network", "mods_enabled": "{0} / {1} enabled", "nodes_connected": "{0} nodes connected", "ready": "Ready", "not_initialized": "Not initialized", "scan_for_mods": "Scan for Mods", "refresh_network": "Refresh Network", "toggle_theme": "Toggle Theme"}, "mod_manager": {"title": "ME3 Mod Manager", "game_path": "Game Path: {0}", "not_detected": "Not detected", "mods_enabled": "{0} / {1} mods enabled", "installed_mods": "Installed Mods", "search_mods": "Search mods...", "mod_details": "<PERSON><PERSON>", "mod_settings": "Mod Manager Set<PERSON>s", "auto_detect": "Auto-detect Game Path", "no_mods_found": "No mods found\n<PERSON><PERSON> 'Refresh' to scan for mods", "not_initialized": "Mod manager not initialized\nCheck game path in settings", "version": "Version: {0}", "author": "Author: {0}", "size": "Size: {0}", "type": "Type: {0}", "status": "Status: {0}", "description": "Description:", "path": "Path: {0}", "no_description": "No description available", "unknown": "Unknown", "unknown_mod": "Unknown Mod", "unknown_size": "Unknown size"}, "network": {"title": "EasyTier Network", "status": "Status", "connected_nodes": "Connected Nodes", "network": "Network:", "local_ip": "Local IP:", "binary": "Binary:", "available": "Available", "not_found": "Not found", "not_connected": "Not connected", "not_assigned": "Not assigned", "nodes": "{0} nodes", "connect_to_network": "Connect to Network", "network_configuration": "Network Configuration", "network_name": "Network Name:", "network_secret": "Network Secret:", "ipv4_address": "IPv4 Address:", "peers": "Peers (one per line):", "auto_assign": "Auto-assign (optional)", "enter_network_name": "Enter network name...", "enter_network_secret": "Enter network secret...", "network_settings": "Network Settings", "binary_path": "EasyTier Binary Path", "listen_port": "Listen Port", "path_to_binary": "Path to easytier-core...", "no_nodes_connected": "No nodes connected\nClick 'Refresh' to update", "not_connected_help": "Not connected to network\nClick 'Connect' to join a network", "latency": "Latency: {0}", "protocol": "Protocol: {0}", "nat": "NAT: {0}", "rx": "RX: {0}", "tx": "TX: {0}", "version": "Version: {0}", "unknown_host": "Unknown", "no_ip": "No IP"}, "settings": {"title": "Settings", "appearance": "Appearance", "application": "Application", "me3_mod_manager": "ME3 Mod Manager", "easytier_network": "EasyTier Network", "about": "About", "theme": "Theme:", "language": "Language:", "auto_start": "Auto-start with system:", "minimize_to_tray": "Minimize to system tray:", "game_path": "Game Path:", "auto_detect_game": "Auto-detect game path:", "enable_backups": "Enable mod backups:", "scan_interval": "Mod scan interval (seconds):", "binary_path": "Binary Path:", "auto_start_network": "Auto-start network:", "default_listen_port": "Default listen port:", "auto_detect": "Auto-detect", "version": "Version:", "build_date": "Build Date:", "qt_version": "Qt Version:", "check_updates": "Check for Updates", "view_logs": "View Logs", "reset_settings": "Reset Settings", "reset_confirmation": "Are you sure you want to reset all settings to default values?", "reset_warning": "This action cannot be undone.", "reset": "Reset", "themes": {"dark": "Dark", "light": "Light", "me3": "Mass Effect 3"}, "languages": {"en": "English", "zh": "中文"}}, "status": {"initializing": "Initializing...", "ready": "Ready", "connecting": "Connecting...", "connected": "Connected", "disconnecting": "Disconnecting...", "disconnected": "Disconnected", "error": "Error", "starting": "Starting...", "stopping": "Stopping...", "stopped": "Stopped"}, "errors": {"mod_manager_init": "Failed to initialize mod manager", "network_manager_init": "Failed to initialize network manager", "game_not_found": "ME3 game installation not found", "binary_not_found": "EasyTier binary not found", "connection_failed": "Failed to connect to network", "mod_scan_failed": "Failed to scan for mods", "config_save_failed": "Failed to save configuration", "config_load_failed": "Failed to load configuration"}}