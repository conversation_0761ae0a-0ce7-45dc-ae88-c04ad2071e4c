"""
NModM 2.0 - ME3 Mod Manager with EasyTier Network Support

A modern Python+QML application for managing Mass Effect 3 mods 
with integrated EasyTier P2P networking capabilities.
"""

__version__ = "2.0.0"
__author__ = "Your Name"
__email__ = "<EMAIL>"

from .core.application import Application
from .core.config import Config
from .core.theme_manager import ThemeManager

__all__ = ["Application", "Config", "ThemeManager"]
