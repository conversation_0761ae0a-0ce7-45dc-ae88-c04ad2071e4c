"""
Configuration management for NModM 2.0
"""

import json
import logging
from pathlib import Path
from typing import Any, Dict, Optional, Union

from PySide6.QtCore import QObject, QSettings, Signal, Slot


class Config(QObject):
    """Application configuration manager."""
    
    # Signals
    configChanged = Signal(str, 'QVariant')  # key, value
    
    _instance: Optional['Config'] = None
    
    def __init__(self, parent: Optional[QObject] = None):
        super().__init__(parent)
        self._logger = logging.getLogger(__name__)
        self._settings = QSettings()
        self._config_dir = Path.home() / ".nmodm2"
        self._config_file = self._config_dir / "config.json"
        
        # Ensure config directory exists
        self._config_dir.mkdir(parents=True, exist_ok=True)
        
        # Default configuration
        self._defaults = {
            # Application settings
            "app": {
                "theme": "dark",
                "language": "en",
                "window_width": 1200,
                "window_height": 800,
                "window_x": -1,
                "window_y": -1,
                "auto_start": False,
                "minimize_to_tray": True,
            },
            
            # ME3 settings
            "me3": {
                "game_path": "",
                "auto_detect": True,
                "backup_enabled": True,
                "backup_path": "",
                "mod_scan_interval": 30,
                "conflict_detection": True,
            },
            
            # EasyTier settings
            "easytier": {
                "binary_path": "",
                "auto_start": False,
                "network_name": "",
                "secret": "",
                "listen_port": 11010,
                "peers": [],
                "dhcp_enabled": True,
                "ipv4": "",
                "ipv6": "",
            },
            
            # Network settings
            "network": {
                "connection_timeout": 30,
                "retry_attempts": 3,
                "status_check_interval": 5,
                "auto_reconnect": True,
            }
        }
        
        # Load configuration
        self._config = self._load_config()
    
    @classmethod
    def instance(cls) -> 'Config':
        """Get singleton instance."""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from file."""
        try:
            if self._config_file.exists():
                with open(self._config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                # Merge with defaults
                merged_config = self._merge_config(self._defaults, config)
                self._logger.info("Configuration loaded successfully")
                return merged_config
            else:
                self._logger.info("No config file found, using defaults")
                return self._defaults.copy()
                
        except Exception as e:
            self._logger.error(f"Failed to load config: {e}")
            return self._defaults.copy()
    
    def _merge_config(self, defaults: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, Any]:
        """Merge configuration with defaults."""
        result = defaults.copy()
        
        for key, value in config.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_config(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def save(self) -> None:
        """Save configuration to file."""
        try:
            with open(self._config_file, 'w', encoding='utf-8') as f:
                json.dump(self._config, f, indent=2, ensure_ascii=False)
            self._logger.info("Configuration saved successfully")
            
        except Exception as e:
            self._logger.error(f"Failed to save config: {e}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value by dot-separated key."""
        keys = key.split('.')
        value = self._config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any) -> None:
        """Set configuration value by dot-separated key."""
        keys = key.split('.')
        config = self._config
        
        # Navigate to parent
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # Set value
        old_value = config.get(keys[-1])
        config[keys[-1]] = value
        
        # Emit signal if value changed
        if old_value != value:
            self.configChanged.emit(key, value)
    
    @Slot(str, result='QVariant')
    def getValue(self, key: str) -> Any:
        """QML-accessible get method."""
        return self.get(key)
    
    @Slot(str, 'QVariant')
    def setValue(self, key: str, value: Any) -> None:
        """QML-accessible set method."""
        self.set(key, value)
    
    @Slot()
    def saveConfig(self) -> None:
        """QML-accessible save method."""
        self.save()
    
    @property
    def config_dir(self) -> Path:
        """Get configuration directory."""
        return self._config_dir
