#!/usr/bin/env python3
"""
Deployment script for NModM 2.0
"""

import os
import sys
import shutil
import zipfile
import platform
import subprocess
from pathlib import Path
from datetime import datetime


def create_deployment_package():
    """Create deployment package."""
    print("Creating deployment package for NModM 2.0...")
    
    # Project paths
    project_root = Path(__file__).parent
    dist_dir = project_root / "dist"
    deploy_dir = project_root / "deploy"
    
    # Clean and create deploy directory
    if deploy_dir.exists():
        shutil.rmtree(deploy_dir)
    deploy_dir.mkdir()
    
    # Platform info
    system = platform.system().lower()
    arch = platform.machine().lower()
    if arch in ["x86_64", "amd64"]:
        arch = "x64"
    elif arch in ["i386", "i686"]:
        arch = "x86"
    
    # Package name
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    package_name = f"NModM2_{system}_{arch}_{timestamp}"
    package_dir = deploy_dir / package_name
    package_dir.mkdir()
    
    # Copy executable
    if system == "windows":
        exe_name = "NModM2.exe"
    else:
        exe_name = "NModM2"
    
    exe_path = dist_dir / exe_name
    if exe_path.exists():
        shutil.copy2(exe_path, package_dir / exe_name)
        print(f"Copied executable: {exe_name}")
    else:
        print(f"Warning: Executable not found: {exe_path}")
    
    # Copy documentation
    docs_to_copy = ["README.md", "LICENSE"]
    for doc in docs_to_copy:
        doc_path = project_root / doc
        if doc_path.exists():
            shutil.copy2(doc_path, package_dir / doc)
            print(f"Copied documentation: {doc}")
    
    # Create installation script
    create_install_script(package_dir, system)
    
    # Create configuration template
    create_config_template(package_dir)
    
    # Create ZIP archive
    zip_path = deploy_dir / f"{package_name}.zip"
    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for file_path in package_dir.rglob("*"):
            if file_path.is_file():
                arcname = file_path.relative_to(package_dir)
                zipf.write(file_path, arcname)
    
    print(f"Created deployment package: {zip_path}")
    
    # Create installer (Windows only)
    if system == "windows":
        create_windows_installer(package_dir)
    
    return zip_path


def create_install_script(package_dir: Path, system: str):
    """Create installation script."""
    if system == "windows":
        script_content = """@echo off
echo Installing NModM 2.0...

REM Create application directory
set INSTALL_DIR=%LOCALAPPDATA%\\NModM2
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

REM Copy executable
copy "NModM2.exe" "%INSTALL_DIR%\\NModM2.exe"

REM Create desktop shortcut
set DESKTOP=%USERPROFILE%\\Desktop
echo [InternetShortcut] > "%DESKTOP%\\NModM 2.0.url"
echo URL=file:///%INSTALL_DIR%\\NModM2.exe >> "%DESKTOP%\\NModM 2.0.url"
echo IconFile=%INSTALL_DIR%\\NModM2.exe >> "%DESKTOP%\\NModM 2.0.url"
echo IconIndex=0 >> "%DESKTOP%\\NModM 2.0.url"

REM Create start menu shortcut
set STARTMENU=%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs
if not exist "%STARTMENU%\\NModM2" mkdir "%STARTMENU%\\NModM2"
echo [InternetShortcut] > "%STARTMENU%\\NModM2\\NModM 2.0.url"
echo URL=file:///%INSTALL_DIR%\\NModM2.exe >> "%STARTMENU%\\NModM2\\NModM 2.0.url"
echo IconFile=%INSTALL_DIR%\\NModM2.exe >> "%STARTMENU%\\NModM2\\NModM 2.0.url"
echo IconIndex=0 >> "%STARTMENU%\\NModM2\\NModM 2.0.url"

echo Installation completed!
echo NModM 2.0 has been installed to: %INSTALL_DIR%
pause
"""
        script_path = package_dir / "install.bat"
        
    else:  # Linux/macOS
        script_content = """#!/bin/bash
echo "Installing NModM 2.0..."

# Create application directory
INSTALL_DIR="$HOME/.local/bin"
mkdir -p "$INSTALL_DIR"

# Copy executable
cp "NModM2" "$INSTALL_DIR/NModM2"
chmod +x "$INSTALL_DIR/NModM2"

# Create desktop entry (Linux only)
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    DESKTOP_DIR="$HOME/.local/share/applications"
    mkdir -p "$DESKTOP_DIR"
    
    cat > "$DESKTOP_DIR/nmodm2.desktop" << EOF
[Desktop Entry]
Name=NModM 2.0
Comment=ME3 Mod Manager with EasyTier Network Support
Exec=$INSTALL_DIR/NModM2
Icon=applications-games
Terminal=false
Type=Application
Categories=Game;Utility;
EOF
fi

echo "Installation completed!"
echo "NModM 2.0 has been installed to: $INSTALL_DIR"
echo "You may need to add $INSTALL_DIR to your PATH"
"""
        script_path = package_dir / "install.sh"
    
    script_path.write_text(script_content, encoding='utf-8')
    
    # Make executable on Unix systems
    if system != "windows":
        os.chmod(script_path, 0o755)
    
    print(f"Created installation script: {script_path.name}")


def create_config_template(package_dir: Path):
    """Create configuration template."""
    config_template = {
        "app": {
            "theme": "dark",
            "language": "en",
            "auto_start": False,
            "minimize_to_tray": True
        },
        "me3": {
            "game_path": "",
            "auto_detect": True,
            "backup_enabled": True,
            "mod_scan_interval": 30
        },
        "easytier": {
            "binary_path": "",
            "auto_start": False,
            "network_name": "",
            "secret": "",
            "listen_port": 11010,
            "peers": [
                "tcp://public.easytier.cn:11010"
            ]
        }
    }
    
    import json
    config_path = package_dir / "config_template.json"
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(config_template, f, indent=2, ensure_ascii=False)
    
    print(f"Created configuration template: {config_path.name}")


def create_windows_installer(package_dir: Path):
    """Create Windows installer using NSIS (if available)."""
    try:
        # Check if NSIS is available
        subprocess.run(["makensis", "/VERSION"], check=True, capture_output=True)
        
        # Create NSIS script
        nsis_script = f"""
!define APPNAME "NModM 2.0"
!define COMPANYNAME "NModM"
!define DESCRIPTION "ME3 Mod Manager with EasyTier Network Support"
!define VERSIONMAJOR 2
!define VERSIONMINOR 0
!define VERSIONBUILD 0

RequestExecutionLevel admin

InstallDir "$LOCALAPPDATA\\NModM2"

Name "${{APPNAME}}"
OutFile "{package_dir.parent / 'NModM2_Setup.exe'}"

Page directory
Page instfiles

Section "install"
    SetOutPath $INSTDIR
    File "{package_dir / 'NModM2.exe'}"
    File "{package_dir / 'README.md'}"
    
    CreateDirectory "$SMPROGRAMS\\NModM2"
    CreateShortCut "$SMPROGRAMS\\NModM2\\NModM 2.0.lnk" "$INSTDIR\\NModM2.exe"
    CreateShortCut "$DESKTOP\\NModM 2.0.lnk" "$INSTDIR\\NModM2.exe"
    
    WriteUninstaller "$INSTDIR\\uninstall.exe"
SectionEnd

Section "uninstall"
    Delete "$INSTDIR\\NModM2.exe"
    Delete "$INSTDIR\\README.md"
    Delete "$INSTDIR\\uninstall.exe"
    RMDir "$INSTDIR"
    
    Delete "$SMPROGRAMS\\NModM2\\NModM 2.0.lnk"
    RMDir "$SMPROGRAMS\\NModM2"
    Delete "$DESKTOP\\NModM 2.0.lnk"
SectionEnd
"""
        
        nsis_path = package_dir.parent / "installer.nsi"
        nsis_path.write_text(nsis_script, encoding='utf-8')
        
        # Compile installer
        subprocess.run(["makensis", str(nsis_path)], check=True)
        print("Created Windows installer: NModM2_Setup.exe")
        
        # Clean up
        nsis_path.unlink()
        
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("NSIS not available, skipping Windows installer creation")


def main():
    """Main deployment function."""
    print("NModM 2.0 Deployment Script")
    print("=" * 40)
    
    # Check if build exists
    project_root = Path(__file__).parent
    dist_dir = project_root / "dist"
    
    if not dist_dir.exists():
        print("Error: dist directory not found. Please run build.py first.")
        return 1
    
    # Create deployment package
    try:
        package_path = create_deployment_package()
        print(f"\nDeployment package created successfully: {package_path}")
        return 0
        
    except Exception as e:
        print(f"Deployment failed: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
