"""
ME3 Mod Scanner
"""

import logging
import json
from pathlib import Path
from typing import List, Optional, Dict, Any

from .mod_model import ModInfo, ModStatus, ModType


class ModScanner:
    """Scans for ME3 mods in the DLC directory."""
    
    def __init__(self):
        self._logger = logging.getLogger(__name__)
        self._game_path = ""
    
    def set_game_path(self, game_path: str) -> None:
        """Set the game path for scanning."""
        self._game_path = game_path
    
    def scan_mods(self, dlc_path: str) -> List[ModInfo]:
        """Scan for mods in the DLC directory."""
        mods = []
        
        try:
            dlc_dir = Path(dlc_path)
            if not dlc_dir.exists():
                self._logger.warning(f"DLC directory not found: {dlc_path}")
                return mods
            
            # Scan for DLC mod folders (start with DLC_MOD_)
            for item in dlc_dir.iterdir():
                if item.is_dir() and item.name.startswith("DLC_MOD_"):
                    mod_info = self._scan_dlc_mod(item)
                    if mod_info:
                        mods.append(mod_info)
                elif item.is_dir() and item.name.startswith("DLC_"):
                    # Check if it's a custom DLC (not official)
                    if self._is_custom_dlc(item):
                        mod_info = self._scan_custom_dlc(item)
                        if mod_info:
                            mods.append(mod_info)
            
            self._logger.info(f"Scanned {len(mods)} mods in {dlc_path}")
            
        except Exception as e:
            self._logger.error(f"Failed to scan mods: {e}")
        
        return mods
    
    def _scan_dlc_mod(self, mod_dir: Path) -> Optional[ModInfo]:
        """Scan a DLC_MOD_ directory."""
        try:
            # Look for moddesc.ini (ME3Tweaks Mod Manager format)
            moddesc_file = mod_dir / "moddesc.ini"
            if moddesc_file.exists():
                return self._parse_moddesc(mod_dir, moddesc_file)
            
            # Look for Mount.dlc
            mount_file = mod_dir / "Mount.dlc"
            if mount_file.exists():
                return self._parse_mount_dlc(mod_dir, mount_file)
            
            # Fallback: create basic mod info
            return self._create_basic_mod_info(mod_dir)
            
        except Exception as e:
            self._logger.error(f"Failed to scan DLC mod {mod_dir.name}: {e}")
            return None
    
    def _scan_custom_dlc(self, dlc_dir: Path) -> Optional[ModInfo]:
        """Scan a custom DLC directory."""
        try:
            # Check for Mount.dlc
            mount_file = dlc_dir / "Mount.dlc"
            if mount_file.exists():
                return self._parse_mount_dlc(dlc_dir, mount_file)
            
            # Create basic info for custom DLC
            return self._create_basic_mod_info(dlc_dir, ModType.CUSTOM_DLC)
            
        except Exception as e:
            self._logger.error(f"Failed to scan custom DLC {dlc_dir.name}: {e}")
            return None
    
    def _parse_moddesc(self, mod_dir: Path, moddesc_file: Path) -> Optional[ModInfo]:
        """Parse ME3Tweaks moddesc.ini file."""
        try:
            import configparser
            
            config = configparser.ConfigParser()
            config.read(moddesc_file, encoding='utf-8')
            
            # Extract mod information
            mod_name = config.get('ModInfo', 'modname', fallback=mod_dir.name)
            mod_version = config.get('ModInfo', 'modver', fallback='1.0.0')
            mod_author = config.get('ModInfo', 'moddev', fallback='Unknown')
            mod_description = config.get('ModInfo', 'moddesc', fallback='')
            mod_site = config.get('ModInfo', 'modsite', fallback='')
            
            # Determine mod type
            mod_type = ModType.DLC_MOD
            if 'FRAMEWORK' in mod_name.upper():
                mod_type = ModType.FRAMEWORK
            elif 'TEXTURE' in mod_name.upper() or 'ALOT' in mod_name.upper():
                mod_type = ModType.TEXTURE
            
            # Check if enabled (directory exists and has files)
            status = ModStatus.ENABLED if self._is_mod_enabled(mod_dir) else ModStatus.DISABLED
            
            return ModInfo(
                name=mod_name,
                version=mod_version,
                author=mod_author,
                description=mod_description,
                mod_type=mod_type,
                status=status,
                path=str(mod_dir),
                size=self._calculate_directory_size(mod_dir),
                website=mod_site,
                dependencies=[],
                conflicts=[]
            )
            
        except Exception as e:
            self._logger.error(f"Failed to parse moddesc.ini: {e}")
            return None
    
    def _parse_mount_dlc(self, mod_dir: Path, mount_file: Path) -> Optional[ModInfo]:
        """Parse Mount.dlc file for mod information."""
        try:
            # Mount.dlc is a binary file, we'll extract basic info
            mod_name = mod_dir.name
            if mod_name.startswith("DLC_MOD_"):
                mod_name = mod_name[8:]  # Remove DLC_MOD_ prefix
            
            # Check for TLK files to get mod name
            tlk_files = list(mod_dir.glob("*.tlk"))
            if tlk_files:
                # Try to extract name from TLK filename
                tlk_name = tlk_files[0].stem
                if "_" in tlk_name:
                    mod_name = tlk_name.split("_")[-1]
            
            status = ModStatus.ENABLED if self._is_mod_enabled(mod_dir) else ModStatus.DISABLED
            
            return ModInfo(
                name=mod_name,
                version="Unknown",
                author="Unknown",
                description=f"Custom DLC mod: {mod_name}",
                mod_type=ModType.DLC_MOD,
                status=status,
                path=str(mod_dir),
                size=self._calculate_directory_size(mod_dir),
                website="",
                dependencies=[],
                conflicts=[]
            )
            
        except Exception as e:
            self._logger.error(f"Failed to parse Mount.dlc: {e}")
            return None
    
    def _create_basic_mod_info(self, mod_dir: Path, mod_type: ModType = ModType.DLC_MOD) -> ModInfo:
        """Create basic mod info for unknown mod format."""
        mod_name = mod_dir.name
        if mod_name.startswith("DLC_MOD_"):
            mod_name = mod_name[8:]  # Remove DLC_MOD_ prefix
        elif mod_name.startswith("DLC_"):
            mod_name = mod_name[4:]  # Remove DLC_ prefix
        
        status = ModStatus.ENABLED if self._is_mod_enabled(mod_dir) else ModStatus.DISABLED
        
        return ModInfo(
            name=mod_name,
            version="Unknown",
            author="Unknown",
            description=f"Detected mod: {mod_name}",
            mod_type=mod_type,
            status=status,
            path=str(mod_dir),
            size=self._calculate_directory_size(mod_dir),
            website="",
            dependencies=[],
            conflicts=[]
        )
    
    def _is_custom_dlc(self, dlc_dir: Path) -> bool:
        """Check if a DLC directory is a custom/mod DLC."""
        # List of known official DLC names
        official_dlcs = {
            "DLC_CON_MP1", "DLC_CON_MP2", "DLC_CON_MP3", "DLC_CON_MP4", "DLC_CON_MP5",
            "DLC_EXP_Pack001", "DLC_EXP_Pack002", "DLC_EXP_Pack003",
            "DLC_HEN_PR", "DLC_OnlinePassHidCE", "DLC_TestPatch",
            "DLC_UPD_Patch01", "DLC_UPD_Patch02"
        }
        
        return dlc_dir.name not in official_dlcs
    
    def _is_mod_enabled(self, mod_dir: Path) -> bool:
        """Check if a mod is currently enabled."""
        # A mod is considered enabled if:
        # 1. The directory exists
        # 2. It contains essential files (Mount.dlc or moddesc.ini)
        # 3. It's not in a disabled state
        
        if not mod_dir.exists():
            return False
        
        # Check for essential files
        essential_files = ["Mount.dlc", "moddesc.ini"]
        has_essential = any((mod_dir / file).exists() for file in essential_files)
        
        # Check for .disabled marker file
        disabled_marker = mod_dir / ".disabled"
        
        return has_essential and not disabled_marker.exists()
    
    def _calculate_directory_size(self, directory: Path) -> int:
        """Calculate total size of directory in bytes."""
        try:
            total_size = 0
            for file_path in directory.rglob("*"):
                if file_path.is_file():
                    total_size += file_path.stat().st_size
            return total_size
        except Exception as e:
            self._logger.debug(f"Failed to calculate directory size: {e}")
            return 0
