"""
EasyTier Network Manager - Complete implementation
"""

import asyncio
import json
import logging
import os
import subprocess
import sys
from pathlib import Path
from typing import Dict, List, Optional, Any
import psutil

from PySide6.QtCore import QObject, Signal, Slot, QTimer, Property, QThread, QProcess

from .easytier_process import EasyTierProcess
from .network_model import NetworkInfo, NodeInfo, ConnectionStatus
from ..utils.file_utils import FileUtils


class NetworkManager(QObject):
    """EasyTier Network Manager - Complete implementation."""

    # Signals
    statusChanged = Signal(str)
    connectionChanged = Signal(bool)
    networkInfoChanged = Signal()
    nodeListChanged = Signal()
    processStateChanged = Signal(str)  # "starting", "running", "stopped", "error"
    errorOccurred = Signal(str, str)  # component, error
    logMessage = Signal(str, str)  # level, message

    def __init__(self, parent: Optional[QObject] = None):
        super().__init__(parent)
        self._logger = logging.getLogger(__name__)
        self._initialized = False
        self._connected = False

        # EasyTier process management
        self._easytier_process: Optional[EasyTierProcess] = None
        self._binary_path = ""
        self._config_file = ""

        # Network state
        self._network_info = NetworkInfo()
        self._nodes: Dict[str, NodeInfo] = {}
        self._connection_status = ConnectionStatus.DISCONNECTED

        # Configuration
        self._network_name = ""
        self._network_secret = ""
        self._listen_port = 11010
        self._peers: List[str] = []
        self._ipv4_address = ""
        self._auto_start = False
        self._dhcp_enabled = True

        # Status monitoring
        self._status_timer = QTimer()
        self._status_timer.timeout.connect(self._update_network_status)
        self._status_timer.setSingleShot(False)

        # Process monitoring
        self._process_timer = QTimer()
        self._process_timer.timeout.connect(self._check_process_status)
        self._process_timer.setSingleShot(False)

    def initialize(self) -> None:
        """Initialize the network manager."""
        try:
            self._logger.info("Initializing EasyTier Network Manager...")

            # Find EasyTier binary
            self._find_easytier_binary()

            # Initialize process manager
            if self._binary_path:
                self._easytier_process = EasyTierProcess(self._binary_path)
                self._easytier_process.processStateChanged.connect(self._on_process_state_changed)
                self._easytier_process.logMessage.connect(self.logMessage)
                self._easytier_process.errorOccurred.connect(self.errorOccurred)

            # Start monitoring timers
            self._status_timer.start(5000)  # Update every 5 seconds
            self._process_timer.start(2000)  # Check process every 2 seconds

            self._initialized = True
            self.statusChanged.emit("Initialized")
            self._logger.info("EasyTier Network Manager initialized successfully")

        except Exception as e:
            self._logger.error(f"Failed to initialize EasyTier Network Manager: {e}")
            self.errorOccurred.emit("NetworkManager", str(e))
            raise

    def _find_easytier_binary(self) -> None:
        """Find EasyTier binary in system."""
        # Common binary names and paths
        binary_names = ["easytier-core", "easytier-core.exe"]
        search_paths = [
            Path.cwd(),
            Path.cwd() / "bin",
            Path.home() / ".local" / "bin",
            Path("/usr/local/bin"),
            Path("/usr/bin"),
            Path("C:/Program Files/EasyTier"),
            Path("C:/Program Files (x86)/EasyTier"),
        ]

        # Add PATH directories
        if "PATH" in os.environ:
            for path_str in os.environ["PATH"].split(os.pathsep):
                search_paths.append(Path(path_str))

        # Search for binary
        for search_path in search_paths:
            for binary_name in binary_names:
                binary_path = search_path / binary_name
                if binary_path.exists() and binary_path.is_file():
                    self._binary_path = str(binary_path)
                    self._logger.info(f"Found EasyTier binary: {self._binary_path}")
                    return

        self._logger.warning("EasyTier binary not found in system")

    def _on_process_state_changed(self, state: str) -> None:
        """Handle process state changes."""
        self.processStateChanged.emit(state)

        if state == "running":
            self._connected = True
            self.connectionChanged.emit(True)
        elif state in ["stopped", "error"]:
            self._connected = False
            self.connectionChanged.emit(False)
            self._nodes.clear()
            self.nodeListChanged.emit()

    def _update_network_status(self) -> None:
        """Update network status periodically."""
        if not self._initialized or not self._easytier_process:
            return

        try:
            # Check if process is running
            if self._easytier_process.is_running():
                # Try to get network information
                self._query_network_info()
            else:
                self._connected = False
                self.connectionChanged.emit(False)

        except Exception as e:
            self._logger.debug(f"Status update failed: {e}")

    def _check_process_status(self) -> None:
        """Check EasyTier process status."""
        if not self._easytier_process:
            return

        try:
            is_running = self._easytier_process.is_running()
            if is_running != self._connected:
                self._connected = is_running
                self.connectionChanged.emit(is_running)

                if is_running:
                    self.statusChanged.emit("Connected")
                else:
                    self.statusChanged.emit("Disconnected")

        except Exception as e:
            self._logger.debug(f"Process status check failed: {e}")

    def _query_network_info(self) -> None:
        """Query network information using easytier-cli."""
        if not self._binary_path:
            return

        try:
            # Try to find easytier-cli
            cli_path = self._binary_path.replace("easytier-core", "easytier-cli")
            if not Path(cli_path).exists():
                return

            # Query peer information
            result = subprocess.run(
                [cli_path, "peer"],
                capture_output=True,
                text=True,
                timeout=5
            )

            if result.returncode == 0:
                self._parse_peer_info(result.stdout)

        except Exception as e:
            self._logger.debug(f"Failed to query network info: {e}")

    def _parse_peer_info(self, output: str) -> None:
        """Parse peer information from easytier-cli output."""
        try:
            # This is a simplified parser - actual implementation would be more robust
            lines = output.strip().split('\n')
            new_nodes = {}

            for line in lines[2:]:  # Skip header lines
                if '|' in line:
                    parts = [p.strip() for p in line.split('|')]
                    if len(parts) >= 10:
                        node_info = NodeInfo(
                            id=parts[9],
                            ipv4=parts[0],
                            hostname=parts[1],
                            cost=parts[2],
                            latency=parts[3],
                            loss_rate=parts[4],
                            rx_bytes=parts[5],
                            tx_bytes=parts[6],
                            tunnel_proto=parts[7],
                            nat_type=parts[8],
                            version=parts[10] if len(parts) > 10 else ""
                        )
                        new_nodes[node_info.id] = node_info

            # Update nodes if changed
            if new_nodes != self._nodes:
                self._nodes = new_nodes
                self.nodeListChanged.emit()

        except Exception as e:
            self._logger.debug(f"Failed to parse peer info: {e}")

    def update_status(self) -> None:
        """Update network manager status."""
        if not self._initialized:
            return

        try:
            if self._connected:
                node_count = len(self._nodes)
                status = f"Connected - {node_count} nodes"
            else:
                status = "Disconnected"

            self.statusChanged.emit(status)

        except Exception as e:
            self._logger.warning(f"Status update failed: {e}")

    def cleanup(self) -> None:
        """Cleanup network manager."""
        self._logger.info("Cleaning up EasyTier Network Manager...")

        # Stop timers
        self._status_timer.stop()
        self._process_timer.stop()

        # Stop EasyTier process
        if self._easytier_process:
            self._easytier_process.stop()
            self._easytier_process = None

        self._initialized = False
        self._connected = False

    # QML-accessible methods
    @Slot(result=bool)
    def isInitialized(self) -> bool:
        """Check if network manager is initialized."""
        return self._initialized

    @Slot(result=bool)
    def isConnected(self) -> bool:
        """Check if network is connected."""
        return self._connected

    @Slot(result=bool)
    def isBinaryAvailable(self) -> bool:
        """Check if EasyTier binary is available."""
        return bool(self._binary_path)

    @Slot(str)
    def setBinaryPath(self, path: str) -> None:
        """Set EasyTier binary path."""
        if Path(path).exists():
            self._binary_path = path
            self._logger.info(f"EasyTier binary path set to: {path}")

    @Slot(result=str)
    def getBinaryPath(self) -> str:
        """Get current binary path."""
        return self._binary_path

    @Slot(str, str, 'QVariant')
    def connect(self, network_name: str, network_secret: str, peers: List[str] = None) -> None:
        """Connect to EasyTier network."""
        try:
            if not self._easytier_process:
                self.errorOccurred.emit("NetworkManager", "EasyTier process not initialized")
                return

            if self._easytier_process.is_running():
                self._logger.warning("EasyTier is already running")
                return

            self._network_name = network_name
            self._network_secret = network_secret
            if peers:
                self._peers = peers

            # Build command arguments
            args = [
                "-d",  # Daemon mode
                "--network-name", network_name,
                "--network-secret", network_secret,
            ]

            # Add IPv4 address if specified
            if self._ipv4_address:
                args.extend(["-i", self._ipv4_address])

            # Add peers
            for peer in self._peers:
                args.extend(["-p", peer])

            # Add listen port
            args.extend(["--listeners", f"tcp://0.0.0.0:{self._listen_port}"])
            args.extend(["--listeners", f"udp://0.0.0.0:{self._listen_port}"])

            # Start process
            self._easytier_process.start(args)
            self._logger.info(f"Connecting to network: {network_name}")

        except Exception as e:
            self._logger.error(f"Failed to connect: {e}")
            self.errorOccurred.emit("Connection", str(e))

    @Slot()
    def disconnect(self) -> None:
        """Disconnect from EasyTier network."""
        try:
            if self._easytier_process:
                self._easytier_process.stop()
                self._logger.info("Disconnecting from network")

        except Exception as e:
            self._logger.error(f"Failed to disconnect: {e}")
            self.errorOccurred.emit("Disconnection", str(e))

    @Slot(result='QVariant')
    def getNodeList(self) -> List[Dict[str, Any]]:
        """Get list of connected nodes."""
        return [node.to_dict() for node in self._nodes.values()]

    @Slot(result='QVariant')
    def getNetworkInfo(self) -> Dict[str, Any]:
        """Get network information."""
        return self._network_info.to_dict()

    @Slot(str)
    def setNetworkName(self, name: str) -> None:
        """Set network name."""
        self._network_name = name

    @Slot(result=str)
    def getNetworkName(self) -> str:
        """Get network name."""
        return self._network_name

    @Slot(str)
    def setNetworkSecret(self, secret: str) -> None:
        """Set network secret."""
        self._network_secret = secret

    @Slot(result=str)
    def getNetworkSecret(self) -> str:
        """Get network secret."""
        return self._network_secret

    @Slot(str)
    def setIPv4Address(self, address: str) -> None:
        """Set IPv4 address."""
        self._ipv4_address = address

    @Slot(result=str)
    def getIPv4Address(self) -> str:
        """Get IPv4 address."""
        return self._ipv4_address

    @Slot(int)
    def setListenPort(self, port: int) -> None:
        """Set listen port."""
        self._listen_port = port

    @Slot(result=int)
    def getListenPort(self) -> int:
        """Get listen port."""
        return self._listen_port

    @Slot('QVariant')
    def setPeers(self, peers: List[str]) -> None:
        """Set peer list."""
        self._peers = peers

    @Slot(result='QVariant')
    def getPeers(self) -> List[str]:
        """Get peer list."""
        return self._peers

    @Slot(str)
    def addPeer(self, peer: str) -> None:
        """Add a peer."""
        if peer not in self._peers:
            self._peers.append(peer)

    @Slot(str)
    def removePeer(self, peer: str) -> None:
        """Remove a peer."""
        if peer in self._peers:
            self._peers.remove(peer)

    @Slot()
    def refreshNodes(self) -> None:
        """Manually refresh node list."""
        self._query_network_info()

    @Slot(result=int)
    def getNodeCount(self) -> int:
        """Get number of connected nodes."""
        return len(self._nodes)

    @Slot(result=str)
    def getConnectionStatus(self) -> str:
        """Get connection status."""
        return self._connection_status.value

    # Properties for QML
    @Property(bool, notify=connectionChanged)
    def connected(self) -> bool:
        return self._connected

    @Property(str, notify=statusChanged)
    def networkName(self) -> str:
        return self._network_name

    @Property(int, notify=nodeListChanged)
    def nodeCount(self) -> int:
        return len(self._nodes)

    @Property(bool, constant=True)
    def binaryAvailable(self) -> bool:
        return bool(self._binary_path)
