"""
Core application class for NModM 2.0
"""

import logging
from typing import Optional
from pathlib import Path

from PySide6.QtCore import QObject, QTimer, Signal, Slot
from PySide6.QtGui import QGuiApplication
from PySide6.QtQml import QQmlApplicationEngine

from .config import Config
from .theme_manager import ThemeManager
from .i18n_manager import I18nManager
from ..me3.mod_manager import ModManager
from ..easytier.network_manager import NetworkManager


class Application(QObject):
    """Main application controller."""
    
    # Signals
    initialized = Signal()
    shutdownRequested = Signal()
    
    def __init__(
        self,
        app: QGuiApplication,
        engine: QQmlApplicationEngine,
        config: Config,
        theme_manager: ThemeManager,
        i18n_manager: I18nManager,
        parent: Optional[QObject] = None
    ):
        super().__init__(parent)
        self._app = app
        self._engine = engine
        self._config = config
        self._theme_manager = theme_manager
        self._i18n_manager = i18n_manager
        self._logger = logging.getLogger(__name__)
        
        # Managers
        self._mod_manager: Optional[ModManager] = None
        self._network_manager: Optional[NetworkManager] = None
        
        # Timers
        self._status_timer = QTimer()
        self._status_timer.timeout.connect(self._update_status)
        
        # Connect signals
        self._app.aboutToQuit.connect(self._on_about_to_quit)
    
    def initialize(self) -> None:
        """Initialize the application."""
        try:
            self._logger.info("Initializing application...")
            
            # Initialize managers
            self._init_managers()
            
            # Setup periodic status updates
            self._status_timer.start(5000)  # Update every 5 seconds
            
            # Apply initial theme
            self._theme_manager.apply_theme()
            
            self.initialized.emit()
            self._logger.info("Application initialized successfully")
            
        except Exception as e:
            self._logger.error(f"Failed to initialize application: {e}")
            raise
    
    def _init_managers(self) -> None:
        """Initialize all managers."""
        # Initialize mod manager
        self._mod_manager = ModManager(self)
        self._mod_manager.initialize()
        
        # Initialize network manager
        self._network_manager = NetworkManager(self)
        self._network_manager.initialize()
        
        # Make managers available to QML
        self._engine.rootContext().setContextProperty("modManager", self._mod_manager)
        self._engine.rootContext().setContextProperty("networkManager", self._network_manager)
    
    @Slot()
    def _update_status(self) -> None:
        """Update application status periodically."""
        try:
            if self._mod_manager:
                self._mod_manager.update_status()
            
            if self._network_manager:
                self._network_manager.update_status()
                
        except Exception as e:
            self._logger.warning(f"Status update failed: {e}")
    
    @Slot()
    def _on_about_to_quit(self) -> None:
        """Handle application shutdown."""
        self._logger.info("Application shutting down...")
        
        # Stop timers
        self._status_timer.stop()
        
        # Cleanup managers
        if self._network_manager:
            self._network_manager.cleanup()
        
        if self._mod_manager:
            self._mod_manager.cleanup()
        
        # Save configuration
        self._config.save()
        
        self.shutdownRequested.emit()
    
    @property
    def mod_manager(self) -> Optional[ModManager]:
        """Get the mod manager instance."""
        return self._mod_manager
    
    @property
    def network_manager(self) -> Optional[NetworkManager]:
        """Get the network manager instance."""
        return self._network_manager
    
    @property
    def config(self) -> Config:
        """Get the configuration instance."""
        return self._config
    
    @property
    def theme_manager(self) -> ThemeManager:
        """Get the theme manager instance."""
        return self._theme_manager

    @property
    def i18n_manager(self) -> I18nManager:
        """Get the i18n manager instance."""
        return self._i18n_manager
