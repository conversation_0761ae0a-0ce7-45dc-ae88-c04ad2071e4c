import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Controls.Material 2.15
import QtQuick.Layouts 1.15
import NModM 1.0

ScrollView {
    id: root
    
    ColumnLayout {
        width: root.width
        spacing: 24
        
        // Header
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: 120
            color: ThemeManager.getColor("surface")
            radius: 12
            
            RowLayout {
                anchors.fill: parent
                anchors.margins: 24
                spacing: 24
                
                // App icon
                Rectangle {
                    width: 72
                    height: 72
                    radius: 36
                    color: ThemeManager.getColor("primary")
                    
                    Text {
                        anchors.centerIn: parent
                        text: "N"
                        color: ThemeManager.getColor("onPrimary")
                        font.pixelSize: 32
                        font.weight: Font.Bold
                    }
                }
                
                // App info
                ColumnLayout {
                    Layout.fillWidth: true
                    spacing: 8
                    
                    Text {
                        text: "NModM 2.0"
                        color: ThemeManager.getColor("textPrimary")
                        font.pixelSize: 28
                        font.weight: Font.Bold
                    }
                    
                    Text {
                        text: "ME3 Mod Manager with EasyTier Network Support"
                        color: ThemeManager.getColor("textSecondary")
                        font.pixelSize: 16
                    }
                    
                    Text {
                        text: "Version " + qmlBridge.appVersion
                        color: ThemeManager.getColor("textSecondary")
                        font.pixelSize: 14
                    }
                }
            }
        }
        
        // Quick stats
        GridLayout {
            Layout.fillWidth: true
            columns: 2
            columnSpacing: 16
            rowSpacing: 16
            
            // ME3 Mods card
            Rectangle {
                Layout.fillWidth: true
                Layout.preferredHeight: 100
                color: ThemeManager.getColor("cardBackground")
                radius: 12
                border.color: ThemeManager.getColor("divider")
                border.width: 1
                
                ColumnLayout {
                    anchors.fill: parent
                    anchors.margins: 16
                    spacing: 8
                    
                    RowLayout {
                        spacing: 12
                        
                        Text {
                            text: "🎮"
                            font.pixelSize: 24
                        }
                        
                        Text {
                            text: "ME3 Mods"
                            color: ThemeManager.getColor("textPrimary")
                            font.pixelSize: 16
                            font.weight: Font.Medium
                        }
                    }
                    
                    Text {
                        text: modManager.enabledModCount + " / " + modManager.modCount + " enabled"
                        color: ThemeManager.getColor("textSecondary")
                        font.pixelSize: 14
                    }
                    
                    Text {
                        text: modManager.isInitialized ? "Ready" : "Not initialized"
                        color: modManager.isInitialized ? "#4CAF50" : "#F44336"
                        font.pixelSize: 12
                    }
                }
            }
            
            // Network card
            Rectangle {
                Layout.fillWidth: true
                Layout.preferredHeight: 100
                color: ThemeManager.getColor("cardBackground")
                radius: 12
                border.color: ThemeManager.getColor("divider")
                border.width: 1
                
                ColumnLayout {
                    anchors.fill: parent
                    anchors.margins: 16
                    spacing: 8
                    
                    RowLayout {
                        spacing: 12
                        
                        Text {
                            text: "🌐"
                            font.pixelSize: 24
                        }
                        
                        Text {
                            text: "Network"
                            color: ThemeManager.getColor("textPrimary")
                            font.pixelSize: 16
                            font.weight: Font.Medium
                        }
                    }
                    
                    Text {
                        text: networkManager.connected ? 
                              networkManager.nodeCount + " nodes connected" : 
                              "Disconnected"
                        color: ThemeManager.getColor("textSecondary")
                        font.pixelSize: 14
                    }
                    
                    Text {
                        text: networkManager.connected ? "Connected" : "Disconnected"
                        color: networkManager.connected ? "#4CAF50" : "#F44336"
                        font.pixelSize: 12
                    }
                }
            }
        }
        
        // Quick actions
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: 200
            color: ThemeManager.getColor("surface")
            radius: 12
            
            ColumnLayout {
                anchors.fill: parent
                anchors.margins: 24
                spacing: 16
                
                Text {
                    text: "Quick Actions"
                    color: ThemeManager.getColor("textPrimary")
                    font.pixelSize: 18
                    font.weight: Font.Medium
                }
                
                GridLayout {
                    Layout.fillWidth: true
                    columns: 2
                    columnSpacing: 16
                    rowSpacing: 12
                    
                    Button {
                        text: "Scan for Mods"
                        Layout.fillWidth: true
                        onClicked: modManager.refreshMods()
                        enabled: modManager.isInitialized
                    }
                    
                    Button {
                        text: "Refresh Network"
                        Layout.fillWidth: true
                        onClicked: networkManager.refreshNodes()
                        enabled: networkManager.connected
                    }
                    
                    Button {
                        text: "Toggle Theme"
                        Layout.fillWidth: true
                        onClicked: {
                            var currentTheme = ThemeManager.getCurrentTheme()
                            var newTheme = currentTheme === "dark" ? "light" : "dark"
                            ThemeManager.setTheme(newTheme)
                            Config.setValue("app.theme", newTheme)
                        }
                    }
                    
                    Button {
                        text: "Settings"
                        Layout.fillWidth: true
                        onClicked: stackView.replace(settingsView)
                    }
                }
            }
        }
        
        // Spacer
        Item {
            Layout.fillHeight: true
        }
    }
}
