"""
Internationalization (i18n) Manager for NModM 2.0
"""

import json
import logging
import re
from pathlib import Path
from typing import Dict, Optional, Any

from PySide6.QtCore import QObject, Signal, Slot, Property


class I18nManager(QObject):
    """Internationalization manager."""
    
    # Signals
    languageChanged = Signal(str)  # language code
    
    _instance: Optional['I18nManager'] = None
    
    def __init__(self, parent: Optional[QObject] = None):
        super().__init__(parent)
        self._logger = logging.getLogger(__name__)
        self._current_language = "en"
        self._translations: Dict[str, Dict[str, Any]] = {}
        self._fallback_language = "en"
        
        # Load available translations
        self._load_translations()
    
    @classmethod
    def instance(cls) -> 'I18nManager':
        """Get singleton instance."""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    def _load_translations(self) -> None:
        """Load all available translation files."""
        try:
            resources_dir = Path(__file__).parent.parent / "resources"
            
            # Load translation files
            translation_files = list(resources_dir.glob("translations_*.json"))
            
            for file_path in translation_files:
                # Extract language code from filename
                match = re.match(r"translations_([a-z]{2})\.json", file_path.name)
                if match:
                    lang_code = match.group(1)
                    
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            translations = json.load(f)
                        
                        self._translations[lang_code] = translations
                        self._logger.info(f"Loaded translations for language: {lang_code}")
                        
                    except Exception as e:
                        self._logger.error(f"Failed to load translations for {lang_code}: {e}")
            
            if not self._translations:
                self._logger.warning("No translation files found")
                # Create minimal fallback translations
                self._translations["en"] = {"common": {"ok": "OK", "cancel": "Cancel"}}
                
        except Exception as e:
            self._logger.error(f"Failed to load translations: {e}")
    
    def set_language(self, language_code: str) -> bool:
        """Set current language."""
        if language_code not in self._translations:
            self._logger.warning(f"Language not available: {language_code}")
            return False
        
        if self._current_language != language_code:
            self._current_language = language_code
            self.languageChanged.emit(language_code)
            self._logger.info(f"Language changed to: {language_code}")
        
        return True
    
    def get_current_language(self) -> str:
        """Get current language code."""
        return self._current_language
    
    def get_available_languages(self) -> Dict[str, str]:
        """Get available languages with their display names."""
        languages = {}
        
        for lang_code in self._translations.keys():
            # Try to get language display name from translations
            display_name = self._get_nested_value(
                self._translations[lang_code], 
                f"settings.languages.{lang_code}"
            )
            
            if not display_name:
                # Fallback to language code
                display_name = lang_code.upper()
            
            languages[lang_code] = display_name
        
        return languages
    
    def translate(self, key: str, *args, language: Optional[str] = None) -> str:
        """Translate a key with optional formatting arguments."""
        if language is None:
            language = self._current_language
        
        # Get translation
        translation = self._get_translation(key, language)
        
        # Format with arguments if provided
        if args:
            try:
                return translation.format(*args)
            except (ValueError, IndexError) as e:
                self._logger.warning(f"Translation formatting failed for key '{key}': {e}")
                return translation
        
        return translation
    
    def _get_translation(self, key: str, language: str) -> str:
        """Get translation for a key in specified language."""
        # Try current language
        if language in self._translations:
            value = self._get_nested_value(self._translations[language], key)
            if value:
                return value
        
        # Try fallback language
        if language != self._fallback_language and self._fallback_language in self._translations:
            value = self._get_nested_value(self._translations[self._fallback_language], key)
            if value:
                return value
        
        # Return key as fallback
        self._logger.debug(f"Translation not found for key: {key}")
        return key
    
    def _get_nested_value(self, data: Dict[str, Any], key: str) -> Optional[str]:
        """Get nested value from dictionary using dot notation."""
        try:
            keys = key.split('.')
            value = data
            
            for k in keys:
                if isinstance(value, dict) and k in value:
                    value = value[k]
                else:
                    return None
            
            return str(value) if value is not None else None
            
        except Exception:
            return None
    
    def has_translation(self, key: str, language: Optional[str] = None) -> bool:
        """Check if translation exists for a key."""
        if language is None:
            language = self._current_language
        
        if language in self._translations:
            return self._get_nested_value(self._translations[language], key) is not None
        
        return False
    
    def get_translation_keys(self, prefix: str = "", language: Optional[str] = None) -> list:
        """Get all translation keys with optional prefix filter."""
        if language is None:
            language = self._current_language
        
        if language not in self._translations:
            return []
        
        keys = []
        self._collect_keys(self._translations[language], "", keys)
        
        if prefix:
            keys = [key for key in keys if key.startswith(prefix)]
        
        return sorted(keys)
    
    def _collect_keys(self, data: Dict[str, Any], prefix: str, keys: list) -> None:
        """Recursively collect all keys from nested dictionary."""
        for key, value in data.items():
            full_key = f"{prefix}.{key}" if prefix else key
            
            if isinstance(value, dict):
                self._collect_keys(value, full_key, keys)
            else:
                keys.append(full_key)
    
    # QML-accessible methods
    @Slot(str, result=str)
    def tr(self, key: str) -> str:
        """QML-accessible translate method."""
        return self.translate(key)
    
    @Slot(str, str, result=str)
    def trArgs1(self, key: str, arg1: str) -> str:
        """QML-accessible translate method with 1 argument."""
        return self.translate(key, arg1)
    
    @Slot(str, str, str, result=str)
    def trArgs2(self, key: str, arg1: str, arg2: str) -> str:
        """QML-accessible translate method with 2 arguments."""
        return self.translate(key, arg1, arg2)
    
    @Slot(str, str, str, str, result=str)
    def trArgs3(self, key: str, arg1: str, arg2: str, arg3: str) -> str:
        """QML-accessible translate method with 3 arguments."""
        return self.translate(key, arg1, arg2, arg3)
    
    @Slot(str)
    def setLanguage(self, language_code: str) -> None:
        """QML-accessible set language method."""
        self.set_language(language_code)
    
    @Slot(result=str)
    def getCurrentLanguage(self) -> str:
        """QML-accessible get current language method."""
        return self.get_current_language()
    
    @Slot(result='QVariant')
    def getAvailableLanguages(self) -> Dict[str, str]:
        """QML-accessible get available languages method."""
        return self.get_available_languages()
    
    @Slot(str, result=bool)
    def hasTranslation(self, key: str) -> bool:
        """QML-accessible has translation method."""
        return self.has_translation(key)
    
    # Properties for QML
    @Property(str, notify=languageChanged)
    def currentLanguage(self) -> str:
        return self._current_language
    
    @Property('QVariant', notify=languageChanged)
    def availableLanguages(self) -> Dict[str, str]:
        return self.get_available_languages()
