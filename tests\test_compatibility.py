#!/usr/bin/env python3
"""
Cross-platform compatibility tests for NModM 2.0
"""

import os
import sys
import platform
import subprocess
import unittest
from pathlib import Path


class CompatibilityTestCase(unittest.TestCase):
    """Test cross-platform compatibility."""
    
    def setUp(self):
        """Set up test environment."""
        self.project_root = Path(__file__).parent.parent
        self.src_dir = self.project_root / "src"
        
    def test_python_version(self):
        """Test Python version compatibility."""
        version = sys.version_info
        self.assertGreaterEqual(version.major, 3, "Python 3 required")
        self.assertGreaterEqual(version.minor, 9, "Python 3.9+ required")
        
    def test_platform_detection(self):
        """Test platform detection."""
        system = platform.system()
        self.assertIn(system, ["Windows", "Linux", "Darwin"], f"Unsupported platform: {system}")
        
    def test_required_modules(self):
        """Test required module imports."""
        required_modules = [
            "PySide6",
            "PySide6.QtCore",
            "PySide6.QtGui", 
            "PySide6.QtQml",
            "PySide6.QtQuickControls2",
            "psutil",
            "watchdog",
            "yaml",
            "requests",
            "send2trash"
        ]
        
        for module in required_modules:
            try:
                __import__(module)
            except ImportError as e:
                self.fail(f"Required module not available: {module} - {e}")
    
    def test_pyside6_functionality(self):
        """Test PySide6 basic functionality."""
        try:
            from PySide6.QtCore import QCoreApplication, QObject, Signal
            from PySide6.QtGui import QGuiApplication
            from PySide6.QtQml import QQmlApplicationEngine
            
            # Test basic Qt functionality
            app = QCoreApplication([])
            self.assertIsNotNone(app)
            
        except Exception as e:
            self.fail(f"PySide6 functionality test failed: {e}")
    
    def test_file_permissions(self):
        """Test file system permissions."""
        # Test read permissions
        self.assertTrue(self.src_dir.exists(), "Source directory not found")
        self.assertTrue(os.access(self.src_dir, os.R_OK), "Source directory not readable")
        
        # Test write permissions in temp directory
        import tempfile
        with tempfile.TemporaryDirectory() as temp_dir:
            test_file = Path(temp_dir) / "test.txt"
            try:
                test_file.write_text("test")
                self.assertTrue(test_file.exists(), "Cannot write to temp directory")
            except Exception as e:
                self.fail(f"File write test failed: {e}")
    
    def test_path_handling(self):
        """Test cross-platform path handling."""
        # Test path creation
        test_path = Path("test") / "path" / "file.txt"
        self.assertIsInstance(test_path, Path)
        
        # Test path resolution
        resolved = test_path.resolve()
        self.assertIsInstance(resolved, Path)
        
        # Test path string conversion
        path_str = str(test_path)
        self.assertIsInstance(path_str, str)
    
    def test_encoding_support(self):
        """Test Unicode and encoding support."""
        # Test Unicode strings
        unicode_text = "测试文本 🎮 Test"
        encoded = unicode_text.encode('utf-8')
        decoded = encoded.decode('utf-8')
        self.assertEqual(unicode_text, decoded)
        
        # Test file encoding
        import tempfile
        with tempfile.NamedTemporaryFile(mode='w', encoding='utf-8', delete=False) as f:
            f.write(unicode_text)
            temp_path = f.name
        
        try:
            with open(temp_path, 'r', encoding='utf-8') as f:
                read_text = f.read()
            self.assertEqual(unicode_text, read_text)
        finally:
            os.unlink(temp_path)
    
    def test_process_management(self):
        """Test process management capabilities."""
        try:
            import psutil
            
            # Test current process info
            current_process = psutil.Process()
            self.assertIsNotNone(current_process.pid)
            self.assertIsNotNone(current_process.name())
            
        except Exception as e:
            self.fail(f"Process management test failed: {e}")
    
    def test_network_capabilities(self):
        """Test network capabilities."""
        try:
            import socket
            
            # Test socket creation
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.close()
            
            # Test hostname resolution
            hostname = socket.gethostname()
            self.assertIsInstance(hostname, str)
            
        except Exception as e:
            self.fail(f"Network capabilities test failed: {e}")
    
    def test_json_handling(self):
        """Test JSON handling."""
        import json
        
        test_data = {
            "string": "test",
            "number": 42,
            "boolean": True,
            "null": None,
            "array": [1, 2, 3],
            "object": {"nested": "value"}
        }
        
        # Test serialization
        json_str = json.dumps(test_data, ensure_ascii=False)
        self.assertIsInstance(json_str, str)
        
        # Test deserialization
        parsed_data = json.loads(json_str)
        self.assertEqual(test_data, parsed_data)
    
    def test_async_support(self):
        """Test async/await support."""
        import asyncio
        
        async def test_coroutine():
            await asyncio.sleep(0.01)
            return "success"
        
        # Test event loop
        loop = asyncio.new_event_loop()
        try:
            result = loop.run_until_complete(test_coroutine())
            self.assertEqual(result, "success")
        finally:
            loop.close()


class WindowsCompatibilityTestCase(unittest.TestCase):
    """Windows-specific compatibility tests."""
    
    def setUp(self):
        """Set up Windows test environment."""
        if platform.system() != "Windows":
            self.skipTest("Windows-specific tests")
    
    def test_windows_registry(self):
        """Test Windows registry access."""
        try:
            import winreg
            
            # Test registry key access
            key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, "SOFTWARE")
            winreg.CloseKey(key)
            
        except Exception as e:
            self.fail(f"Windows registry test failed: {e}")
    
    def test_windows_paths(self):
        """Test Windows path handling."""
        # Test Windows-style paths
        win_path = Path("C:\\Windows\\System32")
        self.assertTrue(win_path.is_absolute())
        
        # Test UNC paths
        unc_path = Path("\\\\server\\share")
        self.assertTrue(unc_path.is_absolute())


class LinuxCompatibilityTestCase(unittest.TestCase):
    """Linux-specific compatibility tests."""
    
    def setUp(self):
        """Set up Linux test environment."""
        if platform.system() != "Linux":
            self.skipTest("Linux-specific tests")
    
    def test_linux_paths(self):
        """Test Linux path handling."""
        # Test Unix-style paths
        unix_path = Path("/usr/bin")
        self.assertTrue(unix_path.is_absolute())
        
        # Test home directory
        home_path = Path.home()
        self.assertTrue(home_path.exists())


def run_compatibility_tests():
    """Run all compatibility tests."""
    print(f"Running compatibility tests on {platform.system()} {platform.release()}")
    print(f"Python {sys.version}")
    print("-" * 60)
    
    # Create test suite
    suite = unittest.TestSuite()
    
    # Add general compatibility tests
    suite.addTest(unittest.makeSuite(CompatibilityTestCase))
    
    # Add platform-specific tests
    if platform.system() == "Windows":
        suite.addTest(unittest.makeSuite(WindowsCompatibilityTestCase))
    elif platform.system() == "Linux":
        suite.addTest(unittest.makeSuite(LinuxCompatibilityTestCase))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_compatibility_tests()
    sys.exit(0 if success else 1)
