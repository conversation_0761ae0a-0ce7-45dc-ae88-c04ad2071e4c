import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Controls.Material 2.15
import QtQuick.Layouts 1.15
import NModM 1.0

Button {
    id: root
    
    property string icon: ""
    property bool selected: false
    
    flat: true
    height: 48
    
    background: Rectangle {
        color: {
            if (root.selected) return ThemeManager.getColor("primary")
            if (root.hovered) return ThemeManager.getColor("divider")
            return "transparent"
        }
        radius: 8
        
        Behavior on color {
            ColorAnimation { duration: 150 }
        }
    }
    
    contentItem: RowLayout {
        spacing: 12
        
        Text {
            text: root.icon
            font.pixelSize: 18
            color: root.selected ? ThemeManager.getColor("onPrimary") : ThemeManager.getColor("textPrimary")
            
            Behavior on color {
                ColorAnimation { duration: 150 }
            }
        }
        
        Text {
            text: root.text
            font.pixelSize: 14
            font.weight: root.selected ? Font.Medium : Font.Normal
            color: root.selected ? ThemeManager.getColor("onPrimary") : ThemeManager.getColor("textPrimary")
            Layout.fillWidth: true
            
            Behavior on color {
                ColorAnimation { duration: 150 }
            }
        }
    }
}
