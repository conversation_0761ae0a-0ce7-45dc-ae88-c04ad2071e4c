import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Controls.Material 2.15
import QtQuick.Layouts 1.15
import NModM 1.0

Control {
    id: root

    property string icon: ""
    property bool selected: false
    property alias text: buttonText.text

    signal clicked()

    width: parent.width
    height: 48

    property bool isHovered: mouseArea.containsMouse
    
    Rectangle {
        anchors.fill: parent
        color: {
            if (root.selected) return ThemeManager.getColor("primary")
            if (root.isHovered) return ThemeManager.getColor("divider")
            return "transparent"
        }
        radius: 8

        Behavior on color {
            ColorAnimation { duration: 150 }
        }

        RowLayout {
            anchors.fill: parent
            anchors.margins: 12
            spacing: 12

            Text {
                text: root.icon
                font.pixelSize: 18
                color: root.selected ? ThemeManager.getColor("onPrimary") : ThemeManager.getColor("textPrimary")

                Behavior on color {
                    ColorAnimation { duration: 150 }
                }
            }

            Text {
                id: buttonText
                font.pixelSize: 14
                font.weight: root.selected ? Font.Medium : Font.Normal
                color: root.selected ? ThemeManager.getColor("onPrimary") : ThemeManager.getColor("textPrimary")
                Layout.fillWidth: true

                Behavior on color {
                    ColorAnimation { duration: 150 }
                }
            }
        }
    }

    MouseArea {
        id: mouseArea
        anchors.fill: parent
        hoverEnabled: true
        onClicked: root.clicked()
    }
}
