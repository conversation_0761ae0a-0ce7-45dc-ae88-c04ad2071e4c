"""
Async Task Manager for background operations
"""

import asyncio
import logging
import threading
from concurrent.futures import ThreadPoolExecutor
from typing import Callable, Any, Optional, Dict
from functools import wraps

from PySide6.QtCore import QObject, Signal, Slot, QTimer, QThread


class AsyncTaskManager(QObject):
    """Manages async tasks and background operations."""
    
    # Signals
    taskStarted = Signal(str)  # task_id
    taskCompleted = Signal(str, 'QVariant')  # task_id, result
    taskFailed = Signal(str, str)  # task_id, error
    taskProgress = Signal(str, int, int)  # task_id, current, total
    
    def __init__(self, max_workers: int = 4, parent: Optional[QObject] = None):
        super().__init__(parent)
        self._logger = logging.getLogger(__name__)
        self._max_workers = max_workers
        
        # Thread pool for CPU-bound tasks
        self._thread_pool = ThreadPoolExecutor(max_workers=max_workers)
        
        # Async event loop for I/O-bound tasks
        self._loop: Optional[asyncio.AbstractEventLoop] = None
        self._loop_thread: Optional[threading.Thread] = None
        
        # Task tracking
        self._running_tasks: Dict[str, Any] = {}
        self._task_counter = 0
        
        # Start async event loop
        self._start_event_loop()
    
    def _start_event_loop(self) -> None:
        """Start the async event loop in a separate thread."""
        def run_loop():
            self._loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self._loop)
            try:
                self._loop.run_forever()
            except Exception as e:
                self._logger.error(f"Event loop error: {e}")
            finally:
                self._loop.close()
        
        self._loop_thread = threading.Thread(target=run_loop, daemon=True)
        self._loop_thread.start()
        
        # Wait for loop to be ready
        while self._loop is None:
            threading.Event().wait(0.01)
    
    def run_async_task(self, coro, task_id: Optional[str] = None) -> str:
        """Run an async coroutine in the background."""
        if task_id is None:
            self._task_counter += 1
            task_id = f"async_task_{self._task_counter}"
        
        if not self._loop or self._loop.is_closed():
            self._logger.error("Event loop not available")
            self.taskFailed.emit(task_id, "Event loop not available")
            return task_id
        
        def task_done_callback(future):
            try:
                result = future.result()
                self.taskCompleted.emit(task_id, result)
            except Exception as e:
                self._logger.error(f"Async task {task_id} failed: {e}")
                self.taskFailed.emit(task_id, str(e))
            finally:
                self._running_tasks.pop(task_id, None)
        
        # Schedule coroutine in event loop
        future = asyncio.run_coroutine_threadsafe(coro, self._loop)
        future.add_done_callback(task_done_callback)
        
        self._running_tasks[task_id] = future
        self.taskStarted.emit(task_id)
        
        return task_id
    
    def run_thread_task(self, func: Callable, *args, task_id: Optional[str] = None, **kwargs) -> str:
        """Run a function in a thread pool."""
        if task_id is None:
            self._task_counter += 1
            task_id = f"thread_task_{self._task_counter}"
        
        def task_wrapper():
            try:
                result = func(*args, **kwargs)
                return result
            except Exception as e:
                self._logger.error(f"Thread task {task_id} failed: {e}")
                raise
        
        def task_done_callback(future):
            try:
                result = future.result()
                self.taskCompleted.emit(task_id, result)
            except Exception as e:
                self.taskFailed.emit(task_id, str(e))
            finally:
                self._running_tasks.pop(task_id, None)
        
        # Submit to thread pool
        future = self._thread_pool.submit(task_wrapper)
        future.add_done_callback(task_done_callback)
        
        self._running_tasks[task_id] = future
        self.taskStarted.emit(task_id)
        
        return task_id
    
    def cancel_task(self, task_id: str) -> bool:
        """Cancel a running task."""
        if task_id not in self._running_tasks:
            return False
        
        try:
            future = self._running_tasks[task_id]
            cancelled = future.cancel()
            
            if cancelled:
                self._running_tasks.pop(task_id, None)
                self._logger.info(f"Task {task_id} cancelled")
            
            return cancelled
            
        except Exception as e:
            self._logger.error(f"Failed to cancel task {task_id}: {e}")
            return False
    
    def get_running_tasks(self) -> list:
        """Get list of running task IDs."""
        return list(self._running_tasks.keys())
    
    def is_task_running(self, task_id: str) -> bool:
        """Check if a task is running."""
        return task_id in self._running_tasks
    
    def cleanup(self) -> None:
        """Cleanup resources."""
        # Cancel all running tasks
        for task_id in list(self._running_tasks.keys()):
            self.cancel_task(task_id)
        
        # Shutdown thread pool
        self._thread_pool.shutdown(wait=True)
        
        # Stop event loop
        if self._loop and not self._loop.is_closed():
            self._loop.call_soon_threadsafe(self._loop.stop)
        
        if self._loop_thread and self._loop_thread.is_alive():
            self._loop_thread.join(timeout=5)
    
    # QML-accessible methods
    @Slot(str, result=bool)
    def cancelTask(self, task_id: str) -> bool:
        """QML-accessible cancel task method."""
        return self.cancel_task(task_id)
    
    @Slot(result='QVariant')
    def getRunningTasks(self) -> list:
        """QML-accessible get running tasks method."""
        return self.get_running_tasks()
    
    @Slot(str, result=bool)
    def isTaskRunning(self, task_id: str) -> bool:
        """QML-accessible is task running method."""
        return self.is_task_running(task_id)


def async_task(task_manager: AsyncTaskManager, task_id: Optional[str] = None):
    """Decorator for async tasks."""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            if asyncio.iscoroutinefunction(func):
                return task_manager.run_async_task(func(*args, **kwargs), task_id)
            else:
                return task_manager.run_thread_task(func, *args, task_id=task_id, **kwargs)
        return wrapper
    return decorator


class ProgressReporter:
    """Helper class for reporting task progress."""
    
    def __init__(self, task_manager: AsyncTaskManager, task_id: str, total: int):
        self.task_manager = task_manager
        self.task_id = task_id
        self.total = total
        self.current = 0
    
    def update(self, increment: int = 1) -> None:
        """Update progress."""
        self.current += increment
        self.task_manager.taskProgress.emit(self.task_id, self.current, self.total)
    
    def set_progress(self, current: int) -> None:
        """Set absolute progress."""
        self.current = current
        self.task_manager.taskProgress.emit(self.task_id, self.current, self.total)
    
    def finish(self) -> None:
        """Mark as finished."""
        self.current = self.total
        self.task_manager.taskProgress.emit(self.task_id, self.current, self.total)


# Example usage functions
async def example_async_task(duration: int = 5) -> str:
    """Example async task."""
    await asyncio.sleep(duration)
    return f"Async task completed after {duration} seconds"


def example_thread_task(duration: int = 5) -> str:
    """Example thread task."""
    import time
    time.sleep(duration)
    return f"Thread task completed after {duration} seconds"


def example_progress_task(task_manager: AsyncTaskManager, task_id: str) -> str:
    """Example task with progress reporting."""
    import time
    
    progress = ProgressReporter(task_manager, task_id, 10)
    
    for i in range(10):
        time.sleep(0.5)  # Simulate work
        progress.update()
    
    return "Progress task completed"
