# Nuitka configuration file for NModM 2.0

[nuitka]
# Basic compilation options
standalone = true
onefile = true

# Plugin configuration
enable-plugin = pyside6,anti-bloat

# Include data directories
include-data-dir = src/nmodm2/qml=nmodm2/qml
include-data-dir = src/nmodm2/resources=nmodm2/resources

# Output configuration
output-dir = build
output-filename = NModM2

# Optimization options
assume-yes-for-downloads = true
remove-output = true
show-progress = true

# Windows specific options
windows-console-mode = disable
windows-icon-from-ico = src/nmodm2/resources/icon.ico

# Performance options
jobs = auto

# Anti-bloat configuration
noinclude-pytest-mode = nofollow
noinclude-setuptools-mode = nofollow
noinclude-unittest-mode = nofollow

# Follow imports
follow-import-to = nmodm2
