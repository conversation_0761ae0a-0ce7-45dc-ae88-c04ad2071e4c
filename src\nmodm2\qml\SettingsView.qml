import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Controls.Material 2.15
import QtQuick.Layouts 1.15
import NModM 1.0

ScrollView {
    id: root
    
    ColumnLayout {
        width: root.width
        spacing: 24
        
        // Header
        Text {
            text: "Settings"
            color: ThemeManager.getColor("textPrimary")
            font.pixelSize: 24
            font.weight: Font.Bold
        }
        
        // Appearance section
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: childrenRect.height + 32
            color: ThemeManager.getColor("surface")
            radius: 12
            
            ColumnLayout {
                anchors.fill: parent
                anchors.margins: 16
                spacing: 16
                
                Text {
                    text: "Appearance"
                    color: ThemeManager.getColor("textPrimary")
                    font.pixelSize: 18
                    font.weight: Font.Medium
                }
                
                RowLayout {
                    Layout.fillWidth: true
                    spacing: 16
                    
                    Text {
                        text: "Theme:"
                        color: ThemeManager.getColor("textSecondary")
                        Layout.preferredWidth: 100
                    }
                    
                    ComboBox {
                        id: themeCombo
                        Layout.fillWidth: true
                        model: ["Dark", "Light", "Mass Effect 3"]
                        currentIndex: {
                            var theme = ThemeManager.getCurrentTheme()
                            switch(theme) {
                                case "dark": return 0
                                case "light": return 1
                                case "me3": return 2
                                default: return 0
                            }
                        }
                        onCurrentIndexChanged: {
                            var themes = ["dark", "light", "me3"]
                            var newTheme = themes[currentIndex]
                            ThemeManager.setTheme(newTheme)
                            Config.setValue("app.theme", newTheme)
                        }
                    }
                }
                
                RowLayout {
                    Layout.fillWidth: true
                    spacing: 16
                    
                    Text {
                        text: "Language:"
                        color: ThemeManager.getColor("textSecondary")
                        Layout.preferredWidth: 100
                    }
                    
                    ComboBox {
                        id: languageCombo
                        Layout.fillWidth: true
                        model: ["English", "中文"]
                        currentIndex: Config.getValue("app.language") === "zh" ? 1 : 0
                        onCurrentIndexChanged: {
                            var languages = ["en", "zh"]
                            Config.setValue("app.language", languages[currentIndex])
                        }
                    }
                }
            }
        }
        
        // Application section
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: childrenRect.height + 32
            color: ThemeManager.getColor("surface")
            radius: 12
            
            ColumnLayout {
                anchors.fill: parent
                anchors.margins: 16
                spacing: 16
                
                Text {
                    text: "Application"
                    color: ThemeManager.getColor("textPrimary")
                    font.pixelSize: 18
                    font.weight: Font.Medium
                }
                
                RowLayout {
                    Layout.fillWidth: true
                    spacing: 16
                    
                    Text {
                        text: "Auto-start with system:"
                        color: ThemeManager.getColor("textSecondary")
                        Layout.fillWidth: true
                    }
                    
                    Switch {
                        checked: Config.getValue("app.auto_start")
                        onToggled: Config.setValue("app.auto_start", checked)
                    }
                }
                
                RowLayout {
                    Layout.fillWidth: true
                    spacing: 16
                    
                    Text {
                        text: "Minimize to system tray:"
                        color: ThemeManager.getColor("textSecondary")
                        Layout.fillWidth: true
                    }
                    
                    Switch {
                        checked: Config.getValue("app.minimize_to_tray")
                        onToggled: Config.setValue("app.minimize_to_tray", checked)
                    }
                }
            }
        }
        
        // ME3 section
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: childrenRect.height + 32
            color: ThemeManager.getColor("surface")
            radius: 12
            
            ColumnLayout {
                anchors.fill: parent
                anchors.margins: 16
                spacing: 16
                
                Text {
                    text: "ME3 Mod Manager"
                    color: ThemeManager.getColor("textPrimary")
                    font.pixelSize: 18
                    font.weight: Font.Medium
                }
                
                RowLayout {
                    Layout.fillWidth: true
                    spacing: 16
                    
                    Text {
                        text: "Game Path:"
                        color: ThemeManager.getColor("textSecondary")
                        Layout.preferredWidth: 100
                    }
                    
                    TextField {
                        id: gamePathField
                        text: Config.getValue("me3.game_path")
                        placeholderText: "Auto-detect"
                        Layout.fillWidth: true
                        onTextChanged: Config.setValue("me3.game_path", text)
                    }
                    
                    Button {
                        text: "Browse"
                        onClicked: {
                            // TODO: Open file dialog
                        }
                    }
                }
                
                RowLayout {
                    Layout.fillWidth: true
                    spacing: 16
                    
                    Text {
                        text: "Auto-detect game path:"
                        color: ThemeManager.getColor("textSecondary")
                        Layout.fillWidth: true
                    }
                    
                    Switch {
                        checked: Config.getValue("me3.auto_detect")
                        onToggled: Config.setValue("me3.auto_detect", checked)
                    }
                }
                
                RowLayout {
                    Layout.fillWidth: true
                    spacing: 16
                    
                    Text {
                        text: "Enable mod backups:"
                        color: ThemeManager.getColor("textSecondary")
                        Layout.fillWidth: true
                    }
                    
                    Switch {
                        checked: Config.getValue("me3.backup_enabled")
                        onToggled: Config.setValue("me3.backup_enabled", checked)
                    }
                }
                
                RowLayout {
                    Layout.fillWidth: true
                    spacing: 16
                    
                    Text {
                        text: "Mod scan interval (seconds):"
                        color: ThemeManager.getColor("textSecondary")
                        Layout.preferredWidth: 200
                    }
                    
                    SpinBox {
                        from: 10
                        to: 300
                        value: Config.getValue("me3.mod_scan_interval")
                        onValueChanged: Config.setValue("me3.mod_scan_interval", value)
                    }
                }
            }
        }
        
        // EasyTier section
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: childrenRect.height + 32
            color: ThemeManager.getColor("surface")
            radius: 12
            
            ColumnLayout {
                anchors.fill: parent
                anchors.margins: 16
                spacing: 16
                
                Text {
                    text: "EasyTier Network"
                    color: ThemeManager.getColor("textPrimary")
                    font.pixelSize: 18
                    font.weight: Font.Medium
                }
                
                RowLayout {
                    Layout.fillWidth: true
                    spacing: 16
                    
                    Text {
                        text: "Binary Path:"
                        color: ThemeManager.getColor("textSecondary")
                        Layout.preferredWidth: 100
                    }
                    
                    TextField {
                        id: binaryPathField
                        text: Config.getValue("easytier.binary_path")
                        placeholderText: "Auto-detect"
                        Layout.fillWidth: true
                        onTextChanged: Config.setValue("easytier.binary_path", text)
                    }
                    
                    Button {
                        text: "Browse"
                        onClicked: {
                            // TODO: Open file dialog
                        }
                    }
                }
                
                RowLayout {
                    Layout.fillWidth: true
                    spacing: 16
                    
                    Text {
                        text: "Auto-start network:"
                        color: ThemeManager.getColor("textSecondary")
                        Layout.fillWidth: true
                    }
                    
                    Switch {
                        checked: Config.getValue("easytier.auto_start")
                        onToggled: Config.setValue("easytier.auto_start", checked)
                    }
                }
                
                RowLayout {
                    Layout.fillWidth: true
                    spacing: 16
                    
                    Text {
                        text: "Default listen port:"
                        color: ThemeManager.getColor("textSecondary")
                        Layout.preferredWidth: 150
                    }
                    
                    SpinBox {
                        from: 1024
                        to: 65535
                        value: Config.getValue("easytier.listen_port")
                        onValueChanged: Config.setValue("easytier.listen_port", value)
                    }
                }
            }
        }
        
        // About section
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: childrenRect.height + 32
            color: ThemeManager.getColor("surface")
            radius: 12
            
            ColumnLayout {
                anchors.fill: parent
                anchors.margins: 16
                spacing: 16
                
                Text {
                    text: "About"
                    color: ThemeManager.getColor("textPrimary")
                    font.pixelSize: 18
                    font.weight: Font.Medium
                }
                
                GridLayout {
                    Layout.fillWidth: true
                    columns: 2
                    columnSpacing: 16
                    rowSpacing: 8
                    
                    Text { text: "Version:"; color: ThemeManager.getColor("textSecondary") }
                    Text { text: qmlBridge.appVersion; color: ThemeManager.getColor("textPrimary") }
                    
                    Text { text: "Build Date:"; color: ThemeManager.getColor("textSecondary") }
                    Text { text: "2025-07-28"; color: ThemeManager.getColor("textPrimary") }
                    
                    Text { text: "Qt Version:"; color: ThemeManager.getColor("textSecondary") }
                    Text { text: "6.6.0"; color: ThemeManager.getColor("textPrimary") }
                }
                
                RowLayout {
                    Layout.fillWidth: true
                    spacing: 12
                    
                    Button {
                        text: "Check for Updates"
                        onClicked: {
                            // TODO: Implement update check
                        }
                    }
                    
                    Button {
                        text: "View Logs"
                        onClicked: {
                            // TODO: Open log viewer
                        }
                    }
                    
                    Button {
                        text: "Reset Settings"
                        onClicked: resetDialog.open()
                    }
                }
            }
        }
        
        // Spacer
        Item {
            Layout.fillHeight: true
        }
    }
    
    // Reset confirmation dialog
    Dialog {
        id: resetDialog
        title: "Reset Settings"
        modal: true
        anchors.centerIn: parent
        width: 350
        height: 200
        
        ColumnLayout {
            anchors.fill: parent
            spacing: 16
            
            Text {
                text: "Are you sure you want to reset all settings to default values?"
                color: ThemeManager.getColor("textPrimary")
                wrapMode: Text.WordWrap
                Layout.fillWidth: true
            }
            
            Text {
                text: "This action cannot be undone."
                color: ThemeManager.getColor("error")
                font.pixelSize: 12
            }
            
            Item { Layout.fillHeight: true }
            
            RowLayout {
                Layout.fillWidth: true
                
                Item { Layout.fillWidth: true }
                
                Button {
                    text: "Cancel"
                    onClicked: resetDialog.close()
                }
                
                Button {
                    text: "Reset"
                    highlighted: true
                    onClicked: {
                        // TODO: Reset all settings
                        resetDialog.close()
                    }
                }
            }
        }
    }
}
