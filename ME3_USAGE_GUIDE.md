# ME3 Mod Manager 使用指南

## 🚀 快速开始

### 1. 启动应用程序
```bash
# 确保在项目根目录
cd E:\PyTest\Nmodm2.0

# 启动ME3 mod管理器
python run.py
```

### 2. 界面概览
启动后您将看到：
- **左侧面板**：检测到的游戏列表和操作按钮
- **右侧面板**：mod列表（初始为空）
- **顶部标题栏**：应用标题和游戏运行状态指示器
- **底部状态栏**：当前操作状态

## 🎮 游戏管理

### 自动游戏检测
应用启动时会自动检测：
1. **Steam游戏**：通过注册表和Steam库文件夹
2. **手动安装**：扫描常见游戏安装目录
3. **ME3兼容性**：检查是否支持ME3 mod引擎

### 游戏选择
1. 在左侧游戏列表中点击游戏
2. 绿色圆圈 ✓ = ME3兼容
3. 橙色圆圈 ! = 部分支持
4. 选中的游戏会高亮显示

### 支持的游戏路径示例
```
C:\Program Files (x86)\Steam\steamapps\common\ELDEN RING\
C:\Program Files (x86)\Steam\steamapps\common\Sekiro\
C:\Program Files (x86)\Steam\steamapps\common\ArmoredCore6\
```

## 🔍 Mod扫描和管理

### 扫描Mod
1. 选择游戏后，点击 **"Scan for Mods"**
2. 系统会扫描以下目录：
   - 游戏根目录
   - `游戏目录/mods/`
   - `游戏目录/mod/`
   - `游戏目录/ModEngine/`

### Mod类型识别
- **R** (红色) = Regulation mod - 游戏参数修改
- **D** (紫色) = DLL mod - 动态链接库
- **A** (蓝色) = Asset mod - 游戏资源文件
- **S** (橙色) = Script mod - 脚本文件

### 启用/禁用Mod
1. **开关控制**：点击mod左侧的开关
2. **状态指示器**：右侧圆点显示当前状态
   - 绿色 = 已启用
   - 红色 = 已禁用

### 右键菜单操作
在mod上右键点击可以：
- **Enable/Disable**：切换mod状态
- **Show in Explorer**：在文件管理器中打开
- **Copy Path**：复制mod路径

## 📁 Mod目录结构示例

### 推荐的目录结构
```
EldenRing/
├── eldenring.exe
├── me3.toml                    # ME3配置文件
├── mods/                       # mod目录
│   ├── SeamlessCoop/          # 无缝合作mod
│   │   ├── mod.json           # mod信息
│   │   ├── seamlesscoop.dll
│   │   └── regulation.bin
│   ├── EldenRingReforged/     # 重制mod
│   │   ├── readme.md
│   │   └── regulation.bin
│   └── disabled/              # 禁用的mod
│       └── OldMod/
└── regulation.bin             # 原版regulation文件
```

### Mod信息文件格式
**mod.json** 示例：
```json
{
    "name": "Seamless Coop",
    "version": "1.7.3",
    "author": "LukeYui",
    "description": "Enables seamless cooperative play",
    "dependencies": [],
    "conflicts": ["OtherCoopMod"]
}
```

## ⚙️ ME3配置管理

### 自动配置创建
选择游戏后，系统会：
1. 查找现有的ME3配置文件
2. 如果不存在，创建默认配置
3. 自动添加mod路径

### 手动配置示例 (me3.toml)
```toml
# 游戏路径
game_path = "C:\\Games\\ELDEN RING"

# 启用ME3
enabled = true

# 调试模式
debug = false

# Mod路径列表
mod_paths = [
    "C:\\Games\\ELDEN RING\\mods",
    "C:\\Games\\ELDEN RING\\regulation"
]

# DLL路径列表
dll_paths = [
    "C:\\Games\\ELDEN RING\\mods\\seamlesscoop.dll"
]
```

## 📊 实时监控

### 启动监控
1. 点击 **"Start Monitoring"** 按钮
2. 状态栏显示 "Started monitoring"
3. 标题栏的状态指示器变为活跃状态

### 游戏进程监控
- **绿色指示器**：游戏正在运行
- **红色指示器**：游戏未运行
- 鼠标悬停显示详细状态

### Mod加载状态
监控会显示：
- 哪些mod成功加载
- 加载失败的mod和错误信息
- 实时的加载进度

### 性能统计
- 内存使用量 (MB)
- CPU使用率 (%)
- 进程运行时间

## 🔧 故障排除

### 游戏检测问题
**问题**：游戏未被检测到
**解决方案**：
1. 确认Steam正确安装
2. 检查游戏是否在支持列表中
3. 手动添加游戏路径

### Mod扫描问题
**问题**：Mod未被扫描到
**解决方案**：
1. 确认mod文件格式正确
2. 检查mod目录权限
3. 查看状态栏错误信息

### 配置文件问题
**问题**：ME3配置错误
**解决方案**：
1. 检查TOML语法
2. 确认文件路径存在
3. 检查文件写入权限

### 监控问题
**问题**：无法监控游戏进程
**解决方案**：
1. 确认游戏可执行文件名正确
2. 检查防火墙设置
3. 以管理员权限运行

## 💡 使用技巧

### 高效Mod管理
1. **分类组织**：将mod按类型放在不同子目录
2. **命名规范**：使用清晰的mod名称
3. **版本控制**：在mod名称中包含版本号
4. **备份重要**：定期备份配置和重要mod

### 性能优化
1. **禁用不需要的mod**：减少加载时间
2. **定期清理**：删除过时的mod文件
3. **监控资源使用**：注意内存和CPU占用

### 安全建议
1. **备份存档**：修改前备份游戏存档
2. **测试mod**：逐个启用测试兼容性
3. **官方更新**：游戏更新后重新测试mod

## 📚 进阶功能

### 批量操作
```python
# 通过Python脚本批量管理
from nmodm.me3.mod_manager import ModManager

manager = ModManager()
manager.setGamePath("C:/Games/EldenRing")

# 批量启用特定类型的mod
for mod in mods:
    if mod.type == "regulation":
        manager.enableMod(mod.name, mod.path)
```

### 自定义配置
```toml
# 高级ME3配置
[advanced]
load_order = ["mod1", "mod2", "mod3"]
backup_enabled = true
auto_update = false

[logging]
level = "debug"
file = "me3_debug.log"
```

### 扩展支持
- 添加新的mod类型检测
- 自定义扫描路径
- 集成其他mod管理工具

这个指南涵盖了ME3 mod管理器的所有主要功能和使用方法。如有问题，请查看状态栏的错误信息或查阅技术文档。
