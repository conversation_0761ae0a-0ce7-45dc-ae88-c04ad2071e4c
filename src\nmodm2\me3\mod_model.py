"""
ME3 Mod Data Models
"""

from enum import Enum
from dataclasses import dataclass, asdict
from typing import List, Dict, Any, Optional
from datetime import datetime


class ModStatus(Enum):
    """Mod status enumeration."""
    ENABLED = "enabled"
    DISABLED = "disabled"
    ERROR = "error"
    INSTALLING = "installing"
    UNINSTALLING = "uninstalling"


class ModType(Enum):
    """Mod type enumeration."""
    DLC_MOD = "dlc_mod"
    CUSTOM_DLC = "custom_dlc"
    TEXTURE = "texture"
    FRAMEWORK = "framework"
    GAMEPLAY = "gameplay"
    AUDIO = "audio"
    OTHER = "other"


@dataclass
class ModInfo:
    """Information about a mod."""
    name: str
    version: str
    author: str
    description: str
    mod_type: ModType
    status: ModStatus
    path: str
    size: int  # Size in bytes
    website: str = ""
    dependencies: List[str] = None
    conflicts: List[str] = None
    install_date: Optional[datetime] = None
    last_updated: Optional[datetime] = None
    
    def __post_init__(self):
        if self.dependencies is None:
            self.dependencies = []
        if self.conflicts is None:
            self.conflicts = []
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for QML."""
        data = asdict(self)
        
        # Convert enums to strings
        data['mod_type'] = self.mod_type.value
        data['status'] = self.status.value
        
        # Convert datetime to ISO string
        if self.install_date:
            data['install_date'] = self.install_date.isoformat()
        if self.last_updated:
            data['last_updated'] = self.last_updated.isoformat()
        
        # Format size as human readable
        data['size_formatted'] = self._format_size(self.size)
        
        return data
    
    def _format_size(self, size_bytes: int) -> str:
        """Format size in human readable format."""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        size = float(size_bytes)
        
        while size >= 1024.0 and i < len(size_names) - 1:
            size /= 1024.0
            i += 1
        
        return f"{size:.1f} {size_names[i]}"
    
    @property
    def is_enabled(self) -> bool:
        """Check if mod is enabled."""
        return self.status == ModStatus.ENABLED
    
    @property
    def has_conflicts(self) -> bool:
        """Check if mod has conflicts."""
        return len(self.conflicts) > 0
    
    @property
    def has_dependencies(self) -> bool:
        """Check if mod has dependencies."""
        return len(self.dependencies) > 0


@dataclass
class ModConflict:
    """Information about mod conflicts."""
    mod1: str
    mod2: str
    conflict_type: str  # "file", "incompatible", "load_order"
    description: str
    severity: str  # "low", "medium", "high", "critical"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for QML."""
        return asdict(self)


@dataclass
class ModDependency:
    """Information about mod dependencies."""
    mod_name: str
    required_mod: str
    min_version: str = ""
    max_version: str = ""
    optional: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for QML."""
        return asdict(self)


class ModCollection:
    """Collection of mods with management capabilities."""
    
    def __init__(self):
        self._mods: Dict[str, ModInfo] = {}
        self._conflicts: List[ModConflict] = []
        self._dependencies: List[ModDependency] = []
    
    def add_mod(self, mod: ModInfo) -> None:
        """Add a mod to the collection."""
        self._mods[mod.name] = mod
    
    def remove_mod(self, mod_name: str) -> bool:
        """Remove a mod from the collection."""
        if mod_name in self._mods:
            del self._mods[mod_name]
            return True
        return False
    
    def get_mod(self, mod_name: str) -> Optional[ModInfo]:
        """Get a mod by name."""
        return self._mods.get(mod_name)
    
    def get_all_mods(self) -> List[ModInfo]:
        """Get all mods."""
        return list(self._mods.values())
    
    def get_enabled_mods(self) -> List[ModInfo]:
        """Get all enabled mods."""
        return [mod for mod in self._mods.values() if mod.is_enabled]
    
    def get_disabled_mods(self) -> List[ModInfo]:
        """Get all disabled mods."""
        return [mod for mod in self._mods.values() if not mod.is_enabled]
    
    def get_mods_by_type(self, mod_type: ModType) -> List[ModInfo]:
        """Get mods by type."""
        return [mod for mod in self._mods.values() if mod.mod_type == mod_type]
    
    def check_conflicts(self) -> List[ModConflict]:
        """Check for conflicts between mods."""
        # This would implement conflict detection logic
        # For now, return stored conflicts
        return self._conflicts
    
    def check_dependencies(self) -> List[ModDependency]:
        """Check mod dependencies."""
        # This would implement dependency checking logic
        # For now, return stored dependencies
        return self._dependencies
    
    def get_load_order(self) -> List[str]:
        """Get recommended load order for mods."""
        # This would implement load order calculation
        # For now, return enabled mods sorted by name
        enabled_mods = self.get_enabled_mods()
        return [mod.name for mod in sorted(enabled_mods, key=lambda x: x.name)]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert collection to dictionary for QML."""
        return {
            'mods': [mod.to_dict() for mod in self._mods.values()],
            'conflicts': [conflict.to_dict() for conflict in self._conflicts],
            'dependencies': [dep.to_dict() for dep in self._dependencies],
            'total_count': len(self._mods),
            'enabled_count': len(self.get_enabled_mods()),
            'disabled_count': len(self.get_disabled_mods()),
        }
