#!/usr/bin/env python3
"""
Build script for NModM 2.0 using Nuitka
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path


def main():
    """Main build function."""
    print("Building NModM 2.0 with Nuitka...")
    
    # Project paths
    project_root = Path(__file__).parent
    src_dir = project_root / "src"
    build_dir = project_root / "build"
    dist_dir = project_root / "dist"
    
    # Clean previous builds
    if build_dir.exists():
        shutil.rmtree(build_dir)
    if dist_dir.exists():
        shutil.rmtree(dist_dir)
    
    # Create directories
    build_dir.mkdir(exist_ok=True)
    dist_dir.mkdir(exist_ok=True)
    
    # Nuitka command
    nuitka_cmd = [
        sys.executable, "-m", "nuitka",
        
        # Basic options
        "--standalone",
        "--onefile",
        
        # Enable plugins
        "--enable-plugin=pyside6",
        "--enable-plugin=anti-bloat",
        
        # Include data files
        f"--include-data-dir={src_dir / 'nmodm2' / 'qml'}=nmodm2/qml",
        f"--include-data-dir={src_dir / 'nmodm2' / 'resources'}=nmodm2/resources",
        
        # Output options
        f"--output-dir={build_dir}",
        "--output-filename=NModM2",
        
        # Optimization
        "--assume-yes-for-downloads",
        "--remove-output",
        
        # Windows specific
        "--windows-console-mode=disable" if sys.platform == "win32" else "",
        f"--windows-icon-from-ico={src_dir / 'nmodm2' / 'resources' / 'icon.ico'}" if sys.platform == "win32" else "",
        
        # Main script
        str(src_dir / "nmodm2" / "main.py")
    ]
    
    # Remove empty strings
    nuitka_cmd = [arg for arg in nuitka_cmd if arg]
    
    print("Running Nuitka command:")
    print(" ".join(nuitka_cmd))
    
    try:
        # Run Nuitka
        result = subprocess.run(nuitka_cmd, check=True, cwd=project_root)
        
        # Move executable to dist directory
        if sys.platform == "win32":
            exe_name = "NModM2.exe"
        else:
            exe_name = "NModM2"
        
        exe_path = build_dir / exe_name
        if exe_path.exists():
            shutil.move(str(exe_path), str(dist_dir / exe_name))
            print(f"Build successful! Executable created: {dist_dir / exe_name}")
        else:
            print("Build completed but executable not found!")
            return 1
        
        return 0
        
    except subprocess.CalledProcessError as e:
        print(f"Build failed with error: {e}")
        return 1
    except Exception as e:
        print(f"Unexpected error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
