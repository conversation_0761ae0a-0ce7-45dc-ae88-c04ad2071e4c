"""
EasyTier Network Data Models
"""

from enum import Enum
from dataclasses import dataclass, asdict
from typing import List, Dict, Any, Optional
from datetime import datetime


class ConnectionStatus(Enum):
    """Network connection status."""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    RECONNECTING = "reconnecting"
    ERROR = "error"


class NodeType(Enum):
    """Node type enumeration."""
    LOCAL = "local"
    PEER = "peer"
    RELAY = "relay"
    PUBLIC = "public"


class TunnelProtocol(Enum):
    """Tunnel protocol enumeration."""
    UDP = "udp"
    TCP = "tcp"
    WS = "ws"
    WSS = "wss"
    QUIC = "quic"
    WG = "wg"


@dataclass
class NodeInfo:
    """Information about a network node."""
    id: str
    ipv4: str
    hostname: str
    cost: str
    latency: str
    loss_rate: str
    rx_bytes: str
    tx_bytes: str
    tunnel_proto: str
    nat_type: str
    version: str = ""
    node_type: NodeType = NodeType.PEER
    last_seen: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for QML."""
        data = asdict(self)
        
        # Convert enums to strings
        data['node_type'] = self.node_type.value
        
        # Convert datetime to ISO string
        if self.last_seen:
            data['last_seen'] = self.last_seen.isoformat()
        
        # Parse numeric values
        data['latency_ms'] = self._parse_latency(self.latency)
        data['loss_rate_percent'] = self._parse_loss_rate(self.loss_rate)
        data['rx_bytes_formatted'] = self._format_bytes(self.rx_bytes)
        data['tx_bytes_formatted'] = self._format_bytes(self.tx_bytes)
        
        return data
    
    def _parse_latency(self, latency_str: str) -> float:
        """Parse latency string to float."""
        try:
            if latency_str == "*" or not latency_str:
                return 0.0
            return float(latency_str)
        except ValueError:
            return 0.0
    
    def _parse_loss_rate(self, loss_rate_str: str) -> float:
        """Parse loss rate string to float."""
        try:
            if loss_rate_str == "*" or not loss_rate_str:
                return 0.0
            return float(loss_rate_str)
        except ValueError:
            return 0.0
    
    def _format_bytes(self, bytes_str: str) -> str:
        """Format bytes string."""
        try:
            if bytes_str == "*" or not bytes_str:
                return "0 B"
            
            # If already formatted, return as is
            if any(unit in bytes_str for unit in ['B', 'KB', 'MB', 'GB']):
                return bytes_str
            
            # Try to parse as number and format
            bytes_num = float(bytes_str)
            return self._format_size(int(bytes_num))
            
        except ValueError:
            return bytes_str
    
    def _format_size(self, size_bytes: int) -> str:
        """Format size in human readable format."""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        size = float(size_bytes)
        
        while size >= 1024.0 and i < len(size_names) - 1:
            size /= 1024.0
            i += 1
        
        return f"{size:.1f} {size_names[i]}"
    
    @property
    def is_local(self) -> bool:
        """Check if this is the local node."""
        return self.node_type == NodeType.LOCAL or self.cost == "Local"
    
    @property
    def is_connected(self) -> bool:
        """Check if node is connected."""
        return self.cost not in ["*", "", "Unknown"]


@dataclass
class NetworkInfo:
    """Information about the network."""
    network_name: str = ""
    network_secret: str = ""
    local_ipv4: str = ""
    local_hostname: str = ""
    listen_ports: List[int] = None
    peers: List[str] = None
    status: ConnectionStatus = ConnectionStatus.DISCONNECTED
    uptime: int = 0  # seconds
    total_nodes: int = 0
    connected_nodes: int = 0
    total_rx_bytes: int = 0
    total_tx_bytes: int = 0
    
    def __post_init__(self):
        if self.listen_ports is None:
            self.listen_ports = []
        if self.peers is None:
            self.peers = []
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for QML."""
        data = asdict(self)
        
        # Convert enums to strings
        data['status'] = self.status.value
        
        # Format uptime
        data['uptime_formatted'] = self._format_uptime(self.uptime)
        
        # Format bytes
        data['total_rx_formatted'] = self._format_size(self.total_rx_bytes)
        data['total_tx_formatted'] = self._format_size(self.total_tx_bytes)
        
        return data
    
    def _format_uptime(self, seconds: int) -> str:
        """Format uptime in human readable format."""
        if seconds < 60:
            return f"{seconds}s"
        elif seconds < 3600:
            minutes = seconds // 60
            return f"{minutes}m {seconds % 60}s"
        elif seconds < 86400:
            hours = seconds // 3600
            minutes = (seconds % 3600) // 60
            return f"{hours}h {minutes}m"
        else:
            days = seconds // 86400
            hours = (seconds % 86400) // 3600
            return f"{days}d {hours}h"
    
    def _format_size(self, size_bytes: int) -> str:
        """Format size in human readable format."""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        size = float(size_bytes)
        
        while size >= 1024.0 and i < len(size_names) - 1:
            size /= 1024.0
            i += 1
        
        return f"{size:.1f} {size_names[i]}"


@dataclass
class NetworkRoute:
    """Information about a network route."""
    destination: str
    gateway: str
    interface: str
    metric: int
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for QML."""
        return asdict(self)


@dataclass
class NetworkStatistics:
    """Network statistics."""
    packets_sent: int = 0
    packets_received: int = 0
    bytes_sent: int = 0
    bytes_received: int = 0
    errors: int = 0
    dropped: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for QML."""
        data = asdict(self)
        
        # Add formatted values
        data['bytes_sent_formatted'] = self._format_size(self.bytes_sent)
        data['bytes_received_formatted'] = self._format_size(self.bytes_received)
        
        return data
    
    def _format_size(self, size_bytes: int) -> str:
        """Format size in human readable format."""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        size = float(size_bytes)
        
        while size >= 1024.0 and i < len(size_names) - 1:
            size /= 1024.0
            i += 1
        
        return f"{size:.1f} {size_names[i]}"


class NetworkCollection:
    """Collection of network information."""
    
    def __init__(self):
        self._network_info = NetworkInfo()
        self._nodes: Dict[str, NodeInfo] = {}
        self._routes: List[NetworkRoute] = []
        self._statistics = NetworkStatistics()
    
    def update_network_info(self, info: NetworkInfo) -> None:
        """Update network information."""
        self._network_info = info
    
    def update_node(self, node: NodeInfo) -> None:
        """Update or add a node."""
        self._nodes[node.id] = node
    
    def remove_node(self, node_id: str) -> bool:
        """Remove a node."""
        if node_id in self._nodes:
            del self._nodes[node_id]
            return True
        return False
    
    def get_node(self, node_id: str) -> Optional[NodeInfo]:
        """Get a node by ID."""
        return self._nodes.get(node_id)
    
    def get_all_nodes(self) -> List[NodeInfo]:
        """Get all nodes."""
        return list(self._nodes.values())
    
    def get_connected_nodes(self) -> List[NodeInfo]:
        """Get all connected nodes."""
        return [node for node in self._nodes.values() if node.is_connected]
    
    def update_routes(self, routes: List[NetworkRoute]) -> None:
        """Update network routes."""
        self._routes = routes
    
    def update_statistics(self, stats: NetworkStatistics) -> None:
        """Update network statistics."""
        self._statistics = stats
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert collection to dictionary for QML."""
        return {
            'network_info': self._network_info.to_dict(),
            'nodes': [node.to_dict() for node in self._nodes.values()],
            'routes': [route.to_dict() for route in self._routes],
            'statistics': self._statistics.to_dict(),
            'total_nodes': len(self._nodes),
            'connected_nodes': len(self.get_connected_nodes()),
        }
