#!/usr/bin/env python3
"""
Main entry point for the NMODM application.
"""

import sys
import os
from pathlib import Path
from PySide6.QtGui import QGuiApplication, QIcon
from PySide6.QtQml import QmlElement, qmlRegisterType
from PySide6.QtQuickControls2 import QQuickStyle

from .core.application import Application
from .core.window_manager import WindowManager
from .core.resource_manager import ResourceManager
from .models.app_model import AppModel
from .me3.game_detector import GameDetector
from .me3.mod_scanner import ModScanner
from .me3.mod_manager import ModManager
from .me3.mod_monitor import ModMonitor
from .easytier.process_manager import EasyTierProcessManager
from .easytier.network_manager import EasyTierNetworkManager
from .easytier.status_monitor import EasyTierStatusMonitor
from .easytier.connection_handler import EasyTierConnectionHandler


QML_IMPORT_NAME = "NmodmApp"
QML_IMPORT_MAJOR_VERSION = 1


def setup_application():
    """Setup the Qt application with proper configuration."""
    # Enable high DPI scaling (if available)
    try:
        QGuiApplication.setHighDpiScaleFactorRoundingPolicy(
            QGuiApplication.HighDpiScaleFactorRoundingPolicy.PassThrough
        )
    except AttributeError:
        # Fallback for older PySide6 versions
        pass

    # Create application instance
    app = QGuiApplication(sys.argv)
    app.setApplicationName("NMODM")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("NMODM Team")
    
    # Set application icon
    icon_path = ResourceManager.get_resource_path("icons/app_icon.png")
    if icon_path.exists():
        app.setWindowIcon(QIcon(str(icon_path)))
    
    # Set Quick Controls style (use Basic to avoid Material issues)
    QQuickStyle.setStyle("Basic")
    
    return app


def register_qml_types():
    """Register custom QML types."""
    # Register core types
    qmlRegisterType(AppModel, QML_IMPORT_NAME, QML_IMPORT_MAJOR_VERSION, 0, "AppModel")
    qmlRegisterType(WindowManager, QML_IMPORT_NAME, QML_IMPORT_MAJOR_VERSION, 0, "WindowManager")

    # Register ME3 mod management types
    qmlRegisterType(GameDetector, QML_IMPORT_NAME, QML_IMPORT_MAJOR_VERSION, 0, "GameDetector")
    qmlRegisterType(ModScanner, QML_IMPORT_NAME, QML_IMPORT_MAJOR_VERSION, 0, "ModScanner")
    qmlRegisterType(ModManager, QML_IMPORT_NAME, QML_IMPORT_MAJOR_VERSION, 0, "ModManager")
    qmlRegisterType(ModMonitor, QML_IMPORT_NAME, QML_IMPORT_MAJOR_VERSION, 0, "ModMonitor")

    # Register EasyTier P2P VPN management types
    qmlRegisterType(EasyTierProcessManager, QML_IMPORT_NAME, QML_IMPORT_MAJOR_VERSION, 0, "EasyTierProcessManager")
    qmlRegisterType(EasyTierNetworkManager, QML_IMPORT_NAME, QML_IMPORT_MAJOR_VERSION, 0, "EasyTierNetworkManager")
    qmlRegisterType(EasyTierStatusMonitor, QML_IMPORT_NAME, QML_IMPORT_MAJOR_VERSION, 0, "EasyTierStatusMonitor")
    qmlRegisterType(EasyTierConnectionHandler, QML_IMPORT_NAME, QML_IMPORT_MAJOR_VERSION, 0, "EasyTierConnectionHandler")


def main():
    """Main application entry point."""
    try:
        # Setup Qt application
        qt_app = QGuiApplication.instance()
        if qt_app is None:
            qt_app = setup_application()
        
        # Register QML types
        register_qml_types()
        
        # Create and run the application
        app = Application()
        
        # Show main window
        if app.show_main_window():
            return qt_app.exec()
        else:
            print("Failed to show main window")
            return 1
            
    except Exception as e:
        print(f"Application error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
