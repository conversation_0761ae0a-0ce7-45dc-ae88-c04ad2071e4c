import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Controls.Material 2.15
import QtQuick.Window 2.15
import QtQuick.Layouts 1.15
import NModM 1.0

ApplicationWindow {
    id: mainWindow
    
    // Window properties
    width: Config.getValue("app.window_width") || 1200
    height: Config.getValue("app.window_height") || 800
    minimumWidth: 800
    minimumHeight: 600
    
    // Frameless window
    flags: Qt.Window | Qt.FramelessWindowHint
    
    // Material theme
    Material.theme: ThemeManager.currentTheme === "light" ? Material.Light : Material.Dark
    Material.primary: ThemeManager.getColor("primary")
    Material.accent: ThemeManager.getColor("accent")
    
    // Window title
    title: "NModM 2.0 - ME3 Mod Manager"
    
    // Background
    color: ThemeManager.getColor("background")
    
    // QML Bridge
    QMLBridge {
        id: qmlBridge
        
        onMessageReceived: function(type, data) {
            console.log("Message received:", type, data)
        }
        
        onErrorOccurred: function(component, error) {
            console.error("Error in", component + ":", error)
        }
    }
    
    // Main content
    ColumnLayout {
        anchors.fill: parent
        spacing: 0
        
        // Custom title bar
        Rectangle {
            id: titleBar
            Layout.fillWidth: true
            Layout.preferredHeight: 40
            color: ThemeManager.getColor("surface")
            
            // Title bar content
            RowLayout {
                anchors.fill: parent
                anchors.leftMargin: 16
                anchors.rightMargin: 8
                
                // App icon and title
                RowLayout {
                    Layout.fillWidth: true
                    spacing: 12
                    
                    Rectangle {
                        width: 24
                        height: 24
                        radius: 12
                        color: ThemeManager.getColor("primary")
                        
                        Text {
                            anchors.centerIn: parent
                            text: "N"
                            color: ThemeManager.getColor("onPrimary")
                            font.pixelSize: 12
                            font.weight: Font.Bold
                        }
                    }
                    
                    Text {
                        text: mainWindow.title
                        color: ThemeManager.getColor("textPrimary")
                        font.pixelSize: 14
                        font.weight: Font.Medium
                    }
                }
                
                // Window controls
                RowLayout {
                    spacing: 4
                    
                    // Minimize button
                    Button {
                        flat: true
                        width: 32
                        height: 32
                        text: "−"
                        font.pixelSize: 16
                        onClicked: qmlBridge.minimizeWindow()
                        
                        background: Rectangle {
                            color: parent.hovered ? ThemeManager.getColor("divider") : "transparent"
                            radius: 4
                        }
                    }
                    
                    // Maximize button
                    Button {
                        flat: true
                        width: 32
                        height: 32
                        text: qmlBridge.isMaximized ? "❐" : "□"
                        font.pixelSize: 12
                        onClicked: qmlBridge.maximizeWindow()
                        
                        background: Rectangle {
                            color: parent.hovered ? ThemeManager.getColor("divider") : "transparent"
                            radius: 4
                        }
                    }
                    
                    // Close button
                    Button {
                        flat: true
                        width: 32
                        height: 32
                        text: "×"
                        font.pixelSize: 18
                        onClicked: qmlBridge.closeWindow()
                        
                        background: Rectangle {
                            color: parent.hovered ? "#E81123" : "transparent"
                            radius: 4
                        }
                        
                        contentItem: Text {
                            text: parent.text
                            color: parent.hovered ? "white" : ThemeManager.getColor("textPrimary")
                            horizontalAlignment: Text.AlignHCenter
                            verticalAlignment: Text.AlignVCenter
                            font: parent.font
                        }
                    }
                }
            }
            
            // Drag area for window movement
            MouseArea {
                anchors.fill: parent
                anchors.rightMargin: 120 // Exclude window controls
                
                property point lastMousePos: Qt.point(0, 0)
                
                onPressed: function(mouse) {
                    lastMousePos = Qt.point(mouse.x, mouse.y)
                }
                
                onMouseXChanged: {
                    if (pressed) {
                        var dx = mouseX - lastMousePos.x
                        mainWindow.x += dx
                    }
                }
                
                onMouseYChanged: {
                    if (pressed) {
                        var dy = mouseY - lastMousePos.y
                        mainWindow.y += dy
                    }
                }
            }
        }
        
        // Main content area
        Rectangle {
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: ThemeManager.getColor("background")
            
            // Welcome content
            ColumnLayout {
                anchors.centerIn: parent
                spacing: 20
                
                Rectangle {
                    width: 80
                    height: 80
                    radius: 40
                    color: ThemeManager.getColor("primary")
                    Layout.alignment: Qt.AlignHCenter
                    
                    Text {
                        anchors.centerIn: parent
                        text: "N"
                        color: ThemeManager.getColor("onPrimary")
                        font.pixelSize: 36
                        font.weight: Font.Bold
                    }
                }
                
                Text {
                    text: "NModM 2.0"
                    font.pixelSize: 32
                    font.weight: Font.Bold
                    color: ThemeManager.getColor("textPrimary")
                    Layout.alignment: Qt.AlignHCenter
                }
                
                Text {
                    text: "ME3 Mod Manager with EasyTier Network Support"
                    font.pixelSize: 16
                    color: ThemeManager.getColor("textSecondary")
                    Layout.alignment: Qt.AlignHCenter
                }
                
                Text {
                    text: "Version " + qmlBridge.appVersion
                    font.pixelSize: 14
                    color: ThemeManager.getColor("textSecondary")
                    Layout.alignment: Qt.AlignHCenter
                }
                
                RowLayout {
                    Layout.alignment: Qt.AlignHCenter
                    spacing: 16
                    
                    Button {
                        text: "Toggle Theme"
                        onClicked: {
                            var currentTheme = ThemeManager.getCurrentTheme()
                            var newTheme = currentTheme === "dark" ? "light" : "dark"
                            ThemeManager.setTheme(newTheme)
                            Config.setValue("app.theme", newTheme)
                        }
                    }
                    
                    Button {
                        text: "Test I18n"
                        onClicked: {
                            var currentLang = I18n.getCurrentLanguage()
                            var newLang = currentLang === "en" ? "zh" : "en"
                            I18n.setLanguage(newLang)
                            Config.setValue("app.language", newLang)
                        }
                    }
                }
                
                Text {
                    text: "Application Status: Ready"
                    font.pixelSize: 12
                    color: "#4CAF50"
                    Layout.alignment: Qt.AlignHCenter
                }
            }
        }
    }
    
    // Window state management
    onWidthChanged: Config.setValue("app.window_width", width)
    onHeightChanged: Config.setValue("app.window_height", height)
    
    Component.onCompleted: {
        // Apply saved theme
        var savedTheme = Config.getValue("app.theme") || "dark"
        ThemeManager.setTheme(savedTheme)
        
        // Apply saved language
        var savedLanguage = Config.getValue("app.language") || "en"
        I18n.setLanguage(savedLanguage)
        
        console.log("NModM 2.0 initialized (Simple Mode)")
    }
}
